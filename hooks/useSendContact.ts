import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const sendContact = async (formData: FormData) => {
    
  const response = await axios.post("/api/contacts", formData, {
    headers: {
        "Content-Type": "application/json",
    },
  });
  return response.data;
};

const useSendContact = () => {
  
  const  {mutate , status } =useMutation({
    mutationFn: sendContact,
    mutationKey: ['contact'],
  });
  return {
    mutate , 
    isLoading: status === 'pending'
  }
};

export default useSendContact;