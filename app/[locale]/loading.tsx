"use client";
import { motion } from "framer-motion";

export default function loading() {
  return (
    <div className="fixed inset-0 bg-[#fffbf1] z-50 flex items-center justify-center">
      <div className="text-center">
        <motion.div
          className="w-16 h-16 border-4 border-[#005274] border-t-transparent rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />
        <p className="mt-4 text-[#003449] text-lg">Loading...</p>
      </div>
    </div>
  );
}
