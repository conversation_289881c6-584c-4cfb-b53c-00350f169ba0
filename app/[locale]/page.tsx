import React from "react";
import { Metadata } from "next";
import Sections from "./Components/SectionsProps";
import TranslationsProvider from "./Components/TranslationsProvider"; // Adjust the path as needed
import initTranslations from "../i18n"; // Import the translation initialization function

interface SectionsProps {
  params: {
    locale: string;
  };
}

const i18nNamespaces = ["default"];

export async function generateMetadata({
  params: { locale },
}: SectionsProps): Promise<Metadata> {
  const { t } = await initTranslations(locale, ["default"]);

  // Set canonical URL based on locale
  const canonicalUrl = locale === 'ar'
    ? "https://www.go-tru.com/ar"
    : "https://www.go-tru.com";

  const title = locale === 'ar' ? 'GoTru - شركة تطوير البرمجيات والاستشارات التقنية' : 'GoTru - Software Development & Tech Consulting Agency';

  return {
    title,
    description: t("seoText.description"),
    openGraph: {
      title: t("seoText.ogTitle"),
      description: t("seoText.ogDescription"),
      url: canonicalUrl,
      siteName: "GoTru",
      images: [
        {
          url: "/icon.webp",
          width: 1200,
          height: 630,
          alt: "GoTru Logo",
        },
      ],
      locale: locale,
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: t("seoText.twitterTitle"),
      description: t("seoText.twitterDescription"),
      images: ["/icon.webp"],
    },
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'en': 'https://www.go-tru.com',
        'ar': 'https://www.go-tru.com/ar',
      },
    },
  };
}


export default async function Home({ params: { locale } }: SectionsProps) {
  const currentLocale = locale;

  const { t, resources } = await initTranslations(
    currentLocale,
    i18nNamespaces
  );

  return (
    <main className="w-full h-full">
      <TranslationsProvider
        namespaces={i18nNamespaces}
        locale={locale}
        resources={resources}
      >
        <Sections t={t} locale={currentLocale} />
      </TranslationsProvider>
    </main>
  );
}
