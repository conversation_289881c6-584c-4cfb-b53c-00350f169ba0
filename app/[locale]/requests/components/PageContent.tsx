import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import NavBar from "../../Components/NavBar";
const PageContent = ({ locale }: { locale: string }) => {
  return (
    <main
      className={`w-full h-[100vh] relative overflow-auto bg-slate-50 ${
        locale === "ar" ? "zain-regular" : "outfit"
      }`}
    >
      <NavBar locale={locale} />
      <div className="flex w-full mt-28  items-center">
        <div className=" flex flex-col gap-3 p-10 w-[60%] rounded-xl  m-auto  bg-white shadow-md">
          <Label>Email Address</Label>
          <Input type="email" placeholder="<EMAIL>" />
        </div>
      </div>
    </main>
  );
};

export default PageContent;
