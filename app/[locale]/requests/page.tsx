import initTranslations from "@/app/i18n";
import React from "react";
import TranslationsProvider from "../Components/TranslationsProvider";
import PageContent from "./components/PageContent";

const RequestsPage = async ({ params }: { params: { locale: string } }) => {
  const i18nNamespaces = ["default"];
  const locale = params.locale;
  const currentLocale = locale;
  const { t, resources } = await initTranslations(
    currentLocale,
    i18nNamespaces
  );
  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale}
      resources={resources}
    >
      <PageContent locale={locale}  />
    </TranslationsProvider>
  );
};

export default RequestsPage;
