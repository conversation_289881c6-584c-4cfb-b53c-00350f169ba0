import { CheckedRole } from "@/app/[locale]/pricing/store/roleStore";
import { CheckedFeature } from "@/app/[locale]/pricing/store/featuresStore";

export const calculateTime = (
  roles: CheckedRole[],
  features: CheckedFeature[],
  platform: string[]
): {
  analizingTime: number;
  developmentTime: number;
  designTime: number;
  devopsTime: number;
  QA_time: number;
  totalTime: number;
} => {


  let design_time = 0;
  let backend_time = 0;
  let web_time = 0;
  let mobile_time = 0;
  let QA_time = 0;
  features.forEach((feature) => {
    if (
      feature.category !== "Support" &&
      feature.category !== "Marketing" &&
      feature.category !== "AddOns"
    ) {
      backend_time += feature.backend_time;
      web_time += feature.web_time;
      mobile_time += feature.mobile_time;
      QA_time += feature.QA_time;
      design_time += feature.design_time;
    }
    if (feature.role_category === "design") {

      design_time += feature.design_time
    }
  });
  let developmentTime = backend_time + web_time + mobile_time;
  let devopsTime = developmentTime * 0.1;
  roles.forEach((role) => {
    if (role.number > 1 && role.category == 'devops') {
      devopsTime *= 0.8;
    } else if (role.number > 1 && !["content", "marketing"].includes(role.category)) {
      developmentTime *= 0.8
    }
  });

  let analizingTime = developmentTime * 0.1;
  if (platform.length > 1) {
    analizingTime *= 1.5
    developmentTime *= 1.5
    design_time *= 1.5
    QA_time *= 1.5

  }

  return {
    analizingTime: Math.trunc(analizingTime * 10) / 10,
    developmentTime: Math.trunc(developmentTime * 10) / 10,
    designTime: Math.trunc(design_time * 10) / 10,
    devopsTime: Math.trunc(devopsTime * 10) / 10,
    QA_time: Math.trunc(QA_time * 10) / 10,
    totalTime:
      design_time +
      QA_time +
      developmentTime +
      analizingTime +
      devopsTime,
  };
};
