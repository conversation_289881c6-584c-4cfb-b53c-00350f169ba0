import { PDFDocument, rgb, StandardFonts } from "pdf-lib";
import { CheckedFeature } from "@/app/[locale]/pricing/store/featuresStore";
import { CheckedRole } from "@/app/[locale]/pricing/store/roleStore";

export async function generatePDF({
  features,
  roles,
  totalTime,
  totalPrice,
}: {
  features: CheckedFeature[];
  roles: CheckedRole[];
  totalTime: number;
  totalPrice: number;
}) {
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage();
  const { width, height } = page.getSize();
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
  const fontSize = 12;
  const boldFontSize = 14;
  let y = height - 40;

  const drawText = (text: string, fontSize: number, font: any) => {
    page.drawText(text, {
      x: 50,
      y,
      size: fontSize,
      font,
      color: rgb(0, 0, 0),
    });
    y -= fontSize + 5; // Add more space between lines
  };

  // Title of the PDF
  drawText("Invoice Details", boldFontSize, boldFont);

  // Add a line break
  y -= 10;

  // Selected Features Section
  drawText("Selected Features:", boldFontSize, boldFont);
  features.forEach((f, i) => {
    drawText(
      `${i + 1}. ${f.title} - ${f.type} - $${f.base_price} - Dev Time: ${
        f.backend_time + f.web_time + f.mobile_time + f.QA_time + f.design_time
      } hrs`,
      fontSize,
      font
    );
  });

  // Add a line break
  y -= 10;

  // Selected Roles Section
  drawText("Selected Roles:", boldFontSize, boldFont);
  roles.forEach((r, i) => {
    drawText(
      `${i + 1}. Role ID: ${r.id} - $${r.cost} x ${r.number}`,
      fontSize,
      font
    );
  });

  // Add a line break
  y -= 10;

  // Total Time and Price Section
  drawText(`Total Time: ${Math.round(totalTime / (8 * 5))} weeks`, fontSize, font);
  drawText(`Total Price: ${totalPrice} AED`, fontSize, font);

  const pdfBytes = await pdfDoc.save();
  return new Blob([pdfBytes], { type: "application/pdf" });
}
