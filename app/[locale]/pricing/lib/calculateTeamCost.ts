import { CheckedFeature } from "@/app/[locale]/pricing/store/featuresStore";
import { CheckedRole } from "@/app/[locale]/pricing/store/roleStore";

export const calculateTeamCost = (
  checkedRoles: CheckedRole[],
  checkedFeatures: CheckedFeature[],
  platform: string[]
) => {


  const teamCost = checkedRoles.reduce((acc, role) => {

    return acc + (role.number > 1 ? role.cost * role.number - role.cost : 0);

  }, 0);

  const basicFeaturesCost = checkedFeatures.reduce(
    (acc, feature) =>
      feature.type === "Basic" ? acc + feature.base_price : acc,
    0
  );

  const extraFeaturesCost = checkedFeatures.reduce(
    (acc, feature) =>
      feature.type === "Optional" ? acc + feature.base_price : acc,
    0
  );
  const addOnsCost = checkedFeatures.reduce(
    (acc, feature) =>
      feature.type === "Add_On" ? acc + feature.base_price : acc,
    0
  );

  const totalPrice =
    basicFeaturesCost + extraFeaturesCost + addOnsCost + teamCost;



  return {
    teamCost: Math.trunc(teamCost * 10) / 10,
    basicFeaturesCost: Math.trunc(basicFeaturesCost * 10) / 10,
    extraFeaturesCost: Math.trunc(extraFeaturesCost * 10) / 10,
    addOnsCost: Math.trunc(addOnsCost * 10) / 10,
    totalPrice: Math.trunc((totalPrice * (platform.length === 2 ? 1.5 : 1)) * 10) / 10,
  };
};
