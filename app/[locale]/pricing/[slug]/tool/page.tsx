import { Metadata } from "next";
import React from "react";
import initTranslations from "@/app/i18n";
import TranslationsProvider from "../../../Components/TranslationsProvider";
import PageContent from "../../components/PageContent";

interface PageProps {
  params: {
    locale: string;
    slug: string;
  };
}

// Function to fetch domain details for metadata
async function fetchDomainDetails(slug: string, locale: string) {
  try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/domains`, {
          method: "GET",
          headers: {
              "Content-Type": "application/json",
              locale: locale,
          },
      });

      if (!response.ok) {
          return null;
      }

      const data = await response.json();
      return data.data.domains.find((domain: any) => domain.slug === slug);
  } catch (error) {
      console.error('Error fetching domain details:', error);
      return null;
  }
}

export const generateMetadata = async ({ params }: PageProps): Promise<Metadata> => {
  const { locale, slug } = params;
  const { t } = await initTranslations(locale, ["default"]);

  // Fetch domain details
  const domain = await fetchDomainDetails(slug, locale);

  // Generate dynamic metadata based on domain and locale
  const baseTitle = locale === "en"
    ? "Interactive Pricing Tool for Software Projects"
    : "أداة التسعير التفاعلية لمشاريع البرمجيات";

  const title = domain
    ? `${domain.title} - ${baseTitle} | GoTru`
    : `GoTru | ${baseTitle}`;

  const baseDescription = locale === "en"
    ? "Explore features and get real-time cost and timeline estimates for your custom software project with GoTru's interactive pricing tool."
    : "استعرض الميزات واحصل على تقديرات فورية للتكلفة والمدة الزمنية لمشروعك البرمجي باستخدام أداة التسعير التفاعلية من GoTru.";

  const description = domain
    ? `${domain.description} ${baseDescription}`
    : baseDescription;

  const url = `https://www.go-tru.com/${locale}/pricing/${slug}/tool`;

  return {
    title,
    description,
    keywords: [
      'GoTru',
      'pricing tool',
      'interactive pricing',
      domain?.title || '',
      locale === 'ar' ? 'أداة التسعير' : 'pricing calculator',
      locale === 'ar' ? 'تطوير' : 'development',
      locale === 'ar' ? 'برمجة' : 'software',
      locale === 'ar' ? 'تطبيقات' : 'applications',
    ].filter(Boolean).join(', '),
    openGraph: {
      title,
      description,
      url,
      siteName: "GoTru",
      images: [
        {
          url: "/icon.webp",
          width: 1200,
          height: 630,
          alt: "GoTru Logo",
        },
      ],
      locale: locale,
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: ["/icon.webp"],
    },
    alternates: {
      canonical: url,
      languages: {
        'en': `https://www.go-tru.com/en/pricing/${slug}/tool`,
        'ar': `https://www.go-tru.com/ar/pricing/${slug}/tool`,
      },
    },
  };
};


const Page = async ({ params: { locale, slug } }: PageProps) => {
  const i18nNamespaces = ["default"];
  const currentLocale = locale;
  const { t, resources } = await initTranslations(currentLocale, i18nNamespaces);

  return (
    <TranslationsProvider namespaces={i18nNamespaces} locale={locale} resources={resources}>
      <PageContent locale={locale} id={slug} />
    </TranslationsProvider>
  );
};

export default Page;
