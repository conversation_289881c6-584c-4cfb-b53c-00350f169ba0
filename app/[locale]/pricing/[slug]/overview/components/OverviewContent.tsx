"use client"
import React from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import Link from 'next/link'
import NavBar from '@/app/[locale]/Components/NavBar'
import Footer from '@/app/[locale]/Components/Footer'
import { useTranslation } from 'react-i18next'
import { useFetchDomainDetails } from '@/app/[locale]/pricing/hooks/useFetchDomainDetails'
import ScreenshotSlider from './ScreenshotSlider'
// Simple loading skeleton component
const Skeleton = ({ className }: { className?: string }) => (
    <div className={`animate-pulse rounded-md bg-gray-200 ${className}`} />
)

interface OverviewContentProps {
    locale: string;
    slug: string;
}

const OverviewContent: React.FC<OverviewContentProps> = ({ locale, slug }) => {
    const isRTL = locale === 'ar'
    const { t } = useTranslation()
    const { domain, features, isLoading, error } = useFetchDomainDetails(slug, locale)

    // Get basic features only for the overview
    const basicFeatures = features.filter(feature => feature.type === 'Basic')

    // Check if this is the medical domain
    const isMedicalDomain = slug === 'medical'

    // Error state
    if (error) {
        return (
            <>
                <NavBar locale={locale} />
                <main className={`w-full max-w-[1700px] mx-auto ${isRTL ? 'zain-regular' : 'outfit'} mt-24`} dir={isRTL ? 'rtl' : 'ltr'}>
                    <div className="flex flex-col items-center justify-center min-h-[50vh] px-6">
                        <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Project Details</h1>
                        <p className="text-gray-600 text-center">
                            We couldn't load the project details. Please try again later or contact support.
                        </p>
                        <Link href={`/${locale}/pricing`} className="mt-4">
                            <Button variant="outline">Back to Pricing</Button>
                        </Link>
                    </div>
                </main>
                <Footer t={t} locale={locale} />
            </>
        )
    }

    const technologies = [
        { name: t("ecommerce.technologies.items.nextjs", "NextJS"), icon: '/icons/nextjs.svg' },
        // { name: t("ecommerce.technologies.items.javascript", "JavaScript"), icon: '/icons/pricing/custom.svg' },
        { name: t("ecommerce.technologies.items.react", "React"), icon: '/icons/React.svg' },
        { name: t("ecommerce.technologies.items.node", "Node"), icon: '/icons/Nodejs.svg' }
    ]

    return (
        <>
            <NavBar locale={locale} />
            <main className={`w-full max-w-[1700px] mx-auto ${isRTL ? 'zain-regular' : 'outfit'}`} dir={isRTL ? 'rtl' : 'ltr'}>
                {/* Hero Section */}
                <section className="relative w-full bg-gradient-to-b from-[#1a5e76] to-[#005171] text-white py-16 px-6 rounded-b-lg pt-32">
                    <div className="max-w-6xl mx-auto">
                        <div className="flex flex-col items-center text-center">
                            <div className="bg-white/10 rounded-full p-4 mb-4">
                                {isLoading ? (
                                    <Skeleton className="w-10 h-10 rounded-full" />
                                ) : (
                                    <Image
                                        src={domain?.icon ? `/icons/pricing/${domain.icon}` : "/icons/pricing/e-commerce.svg"}
                                        alt={domain?.title || "Project"}
                                        width={40}
                                        height={40}
                                        className="w-10 h-10"
                                    />
                                )}
                            </div>
                            {isLoading ? (
                                <Skeleton className="h-12 w-96 mb-4" />
                            ) : (
                                <h1 className="text-3xl md:text-4xl font-bold mb-4">{domain?.title}</h1>
                            )}

                            {isLoading ? (
                                <Skeleton className="h-6 w-full max-w-2xl mb-4" />
                            ) : (
                                <p className="text-sm md:text-base max-w-2xl mb-4">
                                    {domain?.description}
                                </p>
                            )}
                            {/* {!isMedicalDomain && (
                                isLoading ? (
                                    <Skeleton className="h-8 w-48" />*/}
                            {/* domain?.starting_price ? (  */}
                            {slug == "e-commerce" &&
                                <Link className='bg-[#37768E] p-3 rounded-full hover:bg-[#477b8e] transition-colors' href="https://maradi.app/"
                                target="_blanc"
                                >
                                    <p>{t("ecommerce.example")}</p>
                                </Link>

                            }
                            <div className="bg-white/20 rounded-full px-6 py-2 mt-4">
                                <p className="text-lg font-bold">
                                    {t("ecommerce.starting_from", "Starting from")} {domain?.starting_price} {locale === 'ar' ? 'درهم' : 'AED'}
                                </p>
                            </div>
                            {/* ) */}
                            {/* ) : null
                            )} */}
                        </div>
                    </div>
                </section>

                {/* Mobile App Preview Section */}
                <section className="py-16 px-6">
                    <div className="max-w-6xl mx-auto">
                        <div className={`flex flex-col ${isRTL ? 'md:flex-row-reverse' : 'md:flex-row'} items-center gap-8`}>
                            <div className="md:w-1/2 relative flex justify-center">
                                {isLoading ? (
                                    <Skeleton className="w-full max-w-xs h-80 rounded-lg" />
                                ) : domain?.screenshots && domain.screenshots.length > 0 ? (
                                    <ScreenshotSlider
                                        screenshots={domain.screenshots}
                                        alt={domain.title || "Project Screenshots"}
                                        isRTL={isRTL}
                                    />
                                ) : (
                                    <div className="w-full max-w-xs h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                                        <p className="text-gray-500">No screenshots available</p>
                                    </div>
                                )}
                            </div>
                            <div className={`md:w-1/2 ${isRTL ? 'text-right' : ''}`}>
                                {isLoading ? (
                                    <>
                                        <Skeleton className="h-8 w-3/4 mb-4" />
                                        <Skeleton className="h-4 w-full mb-2" />
                                        <Skeleton className="h-4 w-full mb-2" />
                                        <Skeleton className="h-4 w-2/3" />
                                    </>
                                ) : (
                                    <>
                                        <h2 className="text-2xl font-bold text-[#005171] mb-4">
                                            {domain?.preview_title}
                                        </h2>
                                        <p className="text-gray-700 mb-6">
                                            {domain?.preview_description}
                                        </p>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section className="py-12 px-6 bg-gray-50">
                    <div className="max-w-6xl mx-auto">
                        <h2 className={`text-2xl font-bold text-[#005171] mb-8 ${isRTL ? 'text-right' : ''}`}>{t("ecommerce.features.title", "Basic Features")}</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {isLoading ? (
                                // Loading skeletons
                                Array.from({ length: 6 }).map((_, index) => (
                                    <Card key={index} className="border border-[#E5E7EB]">
                                        <CardContent className="pt-6">
                                            <div className="mb-4">
                                                <Skeleton className="h-5 w-3/4 mb-2" />
                                                <Skeleton className="h-4 w-full" />
                                                <Skeleton className="h-4 w-2/3 mt-1" />
                                            </div>
                                        </CardContent>
                                        {!isMedicalDomain && (
                                            <CardFooter className="border-t pt-4">
                                                <Skeleton className="h-5 w-20" />
                                            </CardFooter>
                                        )}
                                    </Card>
                                ))
                            ) : basicFeatures.length > 0 ? (
                                basicFeatures.map((feature, index) => (
                                    <Card key={feature.id || index} className="border border-[#E5E7EB] hover:shadow-md transition-shadow">
                                        <CardContent className="pt-6">
                                            <div className={`mb-4 ${isRTL ? 'text-right' : ''}`}>
                                                <h3 className="font-medium text-[#005171]">{feature.title}</h3>
                                                <p className="text-sm text-gray-600 mt-1">{feature.description}</p>
                                            </div>
                                        </CardContent>
                                        {!isMedicalDomain && (
                                            <CardFooter className="border-t pt-4">
                                                <p className={`text-[#005171] font-bold ${isRTL ? 'w-full text-right' : ''}`}>
                                                    {feature.base_price} {locale === 'ar' ? 'درهم' : 'AED'}
                                                </p>
                                            </CardFooter>
                                        )}
                                    </Card>
                                ))
                            ) : (
                                <div className="col-span-full text-center py-8">
                                    <p className="text-gray-500">{t("ecommerce.features.no_features", "No features available")}</p>
                                </div>
                            )}
                        </div>
                    </div>
                </section>

                {/* Technologies Section */}
                <section className="py-12 px-6">
                    <div className="max-w-6xl mx-auto">
                        <h2 className={`text-2xl font-bold text-[#005171] mb-6 ${isRTL ? 'text-right' : ''}`}>{t("ecommerce.technologies.title", "Technologies")}</h2>
                        <p className={`text-gray-700 mb-8 ${isRTL ? 'text-right' : ''}`}>
                            {t("ecommerce.technologies.description", "We use the latest global technologies like React.js, Node.js, and advanced web development to develop your e-commerce store. These technologies ensure your store is fast, secure, and ready for future growth.")}
                        </p>
                        <div className="flex flex-wrap justify-center gap-8">
                            {technologies.map((tech, index) => (
                                <div key={index} className="flex flex-col items-center">
                                    <div className="w-16 h-16 bg-[#F1F5F9] rounded-full flex items-center justify-center mb-2">
                                        <Image
                                            src={tech.icon}
                                            alt={tech.name}
                                            width={32}
                                            height={32}
                                        />
                                    </div>
                                    <span className="text-sm text-[#005171]">{tech.name}</span>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-16 px-6 text-center">
                    <div className="max-w-3xl mx-auto">
                        <h2 className="text-3xl font-bold mb-4">
                            <span className="text-[#005171]">{t("ecommerce.cta.choose", "Choose")} </span>
                            <span className="text-[#3CA3CC]">{t("ecommerce.cta.company", "GoTru")}</span>
                            <span className="text-[#005171]"> {t("ecommerce.cta.slogan", "Company to make your dreams come true!")}</span>
                        </h2>
                        <div className="flex flex-wrap justify-center gap-4 mt-8">
                            {isMedicalDomain ? (
                                <>
                                    <Button disabled className="bg-gray-400 text-white cursor-not-allowed">
                                        {t("ecommerce.cta.soon", "Soon...")}
                                    </Button>
                                    <Button disabled variant="outline" className="border-gray-400 text-gray-400 cursor-not-allowed">
                                        {t("ecommerce.cta.soon", "Soon...")}
                                    </Button>
                                </>
                            ) : (
                                <>
                                    <Link href={`/${locale}/pricing/${slug}/tool`}>
                                        <Button className="bg-[#005171] hover:bg-[#003449] text-white">
                                            {t("ecommerce.cta.start_button", "Start Project")}
                                        </Button>
                                    </Link>
                                    <Link href={`/${locale}#contactUs`}>
                                        <Button variant="outline" className="border-[#005171] text-[#005171]">
                                            {t("ecommerce.cta.contact_button", "Contact More")}
                                        </Button>
                                    </Link>
                                </>
                            )}
                        </div>
                    </div>
                </section>
            </main>
            <Footer t={t} locale={locale} />
        </>
    )
}

export default OverviewContent
