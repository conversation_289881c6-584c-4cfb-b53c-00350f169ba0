"use client"
import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'

interface ScreenshotSliderProps {
    screenshots: string[];
    alt: string;
    isRTL?: boolean;
}

const ScreenshotSlider: React.FC<ScreenshotSliderProps> = ({ screenshots, alt, isRTL = false }) => {
    const [currentIndex, setCurrentIndex] = useState(0)
    const [isAutoPlaying, setIsAutoPlaying] = useState(true)

    // Auto-play functionality
    useEffect(() => {
        if (!isAutoPlaying || screenshots.length <= 1) return

        const interval = setInterval(() => {
            setCurrentIndex((prevIndex) =>
                prevIndex === screenshots.length - 1 ? 0 : prevIndex + 1
            )
        }, 4000) // Change slide every 4 seconds

        return () => clearInterval(interval)
    }, [isAutoPlaying, screenshots.length])

    const goToPrevious = () => {
        setIsAutoPlaying(false)
        setCurrentIndex(currentIndex === 0 ? screenshots.length - 1 : currentIndex - 1)
        // Resume auto-play after 10 seconds
        setTimeout(() => setIsAutoPlaying(true), 10000)
    }

    const goToNext = () => {
        setIsAutoPlaying(false)
        setCurrentIndex(currentIndex === screenshots.length - 1 ? 0 : currentIndex + 1)
        // Resume auto-play after 10 seconds
        setTimeout(() => setIsAutoPlaying(true), 10000)
    }

    const goToSlide = (index: number) => {
        setIsAutoPlaying(false)
        setCurrentIndex(index)
        // Resume auto-play after 10 seconds
        setTimeout(() => setIsAutoPlaying(true), 10000)
    }

    if (!screenshots || screenshots.length === 0) {
        return (
            <div className="w-full max-w-xs mx-auto h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">No screenshots available</p>
            </div>
        )
    }

    if (screenshots.length === 1) {
        return (
            <div className="relative w-full max-w-xs mx-auto rounded-lg overflow-hidden shadow-lg bg-gray-100">
                <div className="relative w-full p-2">
                    <Image
                        src={screenshots[0]}
                        alt={alt}
                        width={300}
                        height={600}
                        className="w-full h-auto object-contain"
                        priority
                    />
                </div>
            </div>
        )
    }

    return (
        <div className="relative w-full max-w-xs mx-auto rounded-lg overflow-hidden shadow-lg group bg-gray-100">
            {/* Main Image */}
            <div className="relative w-full p-2">
                <Image
                    src={screenshots[currentIndex]}
                    alt={`${alt} - Screenshot ${currentIndex + 1}`}
                    width={300}
                    height={600}
                    className="w-full h-auto object-contain transition-opacity duration-500"
                    priority={currentIndex === 0}
                />
            </div>

            {/* Navigation Arrows */}
            <button
                onClick={goToPrevious}
                className={`absolute top-1/2 transform -translate-y-1/2 ${isRTL ? 'right-4' : 'left-4'}
                    bg-black/50 hover:bg-black/70 text-white p-2 rounded-full
                    opacity-0 group-hover:opacity-100 transition-opacity duration-300
                    focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-white/50`}
                aria-label="Previous screenshot"
            >
                {isRTL ? (
                    <ChevronRightIcon className="w-5 h-5" />
                ) : (
                    <ChevronLeftIcon className="w-5 h-5" />
                )}
            </button>

            <button
                onClick={goToNext}
                className={`absolute top-1/2 transform -translate-y-1/2 ${isRTL ? 'left-4' : 'right-4'}
                    bg-black/50 hover:bg-black/70 text-white p-2 rounded-full
                    opacity-0 group-hover:opacity-100 transition-opacity duration-300
                    focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-white/50`}
                aria-label="Next screenshot"
            >
                {isRTL ? (
                    <ChevronLeftIcon className="w-5 h-5" />
                ) : (
                    <ChevronRightIcon className="w-5 h-5" />
                )}
            </button>

            {/* Dots Indicator */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                {screenshots.map((_, index) => (
                    <button
                        key={index}
                        onClick={() => goToSlide(index)}
                        className={`w-3 h-3 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white/50 ${
                            index === currentIndex
                                ? 'bg-white scale-110'
                                : 'bg-white/50 hover:bg-white/70'
                        }`}
                        aria-label={`Go to screenshot ${index + 1}`}
                    />
                ))}
            </div>

            {/* Auto-play indicator */}
            {isAutoPlaying && screenshots.length > 1 && (
                <div className="absolute top-4 right-4 bg-black/50 text-white px-2 py-1 rounded text-xs">
                    Auto-play
                </div>
            )}

            {/* Screenshot counter */}
            <div className="absolute top-4 left-4 bg-black/50 text-white px-2 py-1 rounded text-xs">
                {currentIndex + 1} / {screenshots.length}
            </div>
        </div>
    )
}

export default ScreenshotSlider
