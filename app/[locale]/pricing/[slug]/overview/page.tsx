import React from 'react'
import { Metadata } from 'next'
import TranslationsProvider from '@/app/[locale]/Components/TranslationsProvider'
import initTranslations from '@/app/i18n'
import OverviewContent from './components/OverviewContent'

interface PageProps {
    params: {
        locale: string;
        slug: string;
    }
}

// Function to fetch domain details for metadata
async function fetchDomainDetails(slug: string, locale: string) {
    try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/domains`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                locale: locale,
            },
        });

        if (!response.ok) {
            return null;
        }

        const data = await response.json();
        return data.data.domains.find((domain: any) => domain.slug === slug);
    } catch (error) {
        console.error('Error fetching domain details:', error);
        return null;
    }
}

// Generate metadata for each pricing page
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
    const { locale, slug } = params;
    const { t } = await initTranslations(locale, ["default"]);

    // Fetch domain details
    const domain = await fetchDomainDetails(slug, locale);

    // Default metadata
    const defaultTitle = t("pricing_section.title");
    const defaultDescription = t("pricing_section.subtitle");

    // Enhanced SEO metadata
    const title = domain
        ? (locale === 'ar'
            ? `أسعار ${domain.title} | تكلفة التطوير والميزات | GoTru`
            : `${domain.title} Pricing | Development Costs & Features | GoTru`)
        : `${defaultTitle} | GoTru`;

    const description = domain
        ? (locale === 'ar'
            ? `احصل على أسعار مفصلة لـ ${domain.title}. ${domain.description} مع أسعار شفافة وبدون رسوم خفية. عروض أسعار مجانية متاحة.`
            : `Get detailed ${domain.title.toLowerCase()} pricing. ${domain.description} Transparent costs with no hidden fees. Free quotes available.`)
        : defaultDescription;

    const url = `https://www.go-tru.com/${locale}/pricing/${slug}/overview`;

    return {
        title,
        description,
        keywords: domain
            ? (locale === 'ar'
                ? `أسعار ${domain.title}, تكلفة ${domain.title}, ${domain.title} أسعار, تطوير ${domain.title}, أسعار تطوير البرمجيات, GoTru أسعار, حاسبة تكلفة التطوير`
                : `${domain.title.toLowerCase()} pricing, ${domain.title.toLowerCase()} development cost, ${domain.title.toLowerCase()} rates, software development pricing, ${domain.title.toLowerCase()} cost calculator, GoTru pricing, development cost estimate`)
            : (locale === 'ar' ? 'أسعار تطوير البرمجيات' : 'software development pricing'),
        openGraph: {
            title,
            description,
            url,
            siteName: "GoTru",
            images: [
                {
                    url: "/icon.webp",
                    width: 1200,
                    height: 630,
                    alt: "GoTru Logo",
                },
            ],
            locale: locale,
            type: "website",
        },
        twitter: {
            card: "summary_large_image",
            title,
            description,
            images: ["/icon.webp"],
        },
        alternates: {
            canonical: url,
            languages: {
                'en': `https://www.go-tru.com/pricing/${slug}/overview`,
                'ar': `https://www.go-tru.com/ar/pricing/${slug}/overview`,
            },
        },
    };
}

const OverviewPage = async ({ params }: PageProps) => {
    const { locale, slug } = params
    const i18nNamespaces = ["default"]
    const { resources } = await initTranslations(locale, i18nNamespaces)

    return (
        <TranslationsProvider
            namespaces={i18nNamespaces}
            locale={locale}
            resources={resources}
        >
            <OverviewContent locale={locale} slug={slug} />
        </TranslationsProvider>
    )
}

export default OverviewPage