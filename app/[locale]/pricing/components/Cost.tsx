"use client";

const Cost = ({
  cost,
  title,
  loading,
  isRTL,
}: {
  cost: number;
  title: string;
  loading: boolean;
  isRTL: boolean;
}) => {

  return (
    <div
      className={`flex justify-between text-sm ${
        isRTL ? "text-right flex-row-reverse" : "text-left"
      }`}
    >
      <p>{title}</p>
      <p>{loading ? "calculating.." : `+ ${cost} AED`}</p>
    </div>
  );
};

export default Cost;
