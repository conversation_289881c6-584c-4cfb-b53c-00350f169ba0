"use client";
import React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useFeatureStore } from "@/app/[locale]/pricing/store/featuresStore";
import { useRoleStore } from "@/app/[locale]/pricing/store/roleStore";
import { useStepStore } from "@/app/[locale]/pricing/store/useStepStore";
import { useTranslation } from "react-i18next";

const Platform = ({ locale }: { locale: string }) => {
  const { platform, togglePlatform } = useFeatureStore();
  const { toggleRole, roles } = useRoleStore();
  const { setActiveStep } = useStepStore();
  const { t } = useTranslation();

  const PLATFORM_STEP_INDEX = 0;


  const handlePlatformCheck = (plat: string) => {

    togglePlatform(plat);
    if (plat == "mobile") {


      toggleRole(mobile?.id!, mobile?.title!, mobile?.description!, mobile?.category!, mobile?.cost!)
      if (platform.length == 0) {

      }
    } else {


      toggleRole(web?.id!, web?.title!, web?.description!, web?.category!, web?.cost!);

    }
    handlePlatformClick();
  }
  const handlePlatformClick = () => {
    setActiveStep(PLATFORM_STEP_INDEX);
  };
  const web = roles?.find((role) => role?.category === 'web');
  const mobile = roles?.find((role) => role?.category === 'mobile');
  const isRTL = locale == "ar";
  return (
    <div>
      <div className={`${isRTL ? "text-right" : ""}`}>
        <div
          className={`flex justify-start items-center gap-2 ${isRTL ? "flex-row-reverse" : ""
            }`}
        >
          <div className="h-5 w-[3px] bg-[#A3E635]" />
          <p className="text-[#005171] font-bold">
            {t("pricing_system.platform.title")}
          </p>
        </div>
        <p className="text-sm text-[#00517187]">
          {t("pricing_system.platform.description")}
        </p>
      </div>
      <div className="flex justify-between mt-4 px-4 gap-2">
        <div
          className="flex border-1 w-full rounded-lg border-blue-600 gap-3 items-center p-3 cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={handlePlatformClick}
        >
          <Checkbox
            id="web"
            value="web"
            checked={platform.includes("web")}
            onCheckedChange={(checked) => {
              if (!checked && platform.length === 1 && platform.includes("web")) {
                // Auto-check mobile if user tries to uncheck web
                togglePlatform("mobile");
                toggleRole(
                  mobile?.id!,
                  mobile?.title!,
                  mobile?.description!,
                  mobile?.category!,
                  mobile?.cost!
                );
              }

              togglePlatform("web");
              toggleRole(
                web?.id!,
                web?.title!,
                web?.description!,
                web?.category!,
                web?.cost!
              );
              handlePlatformClick();
            }}
          />
          <Label htmlFor="web" className="cursor-pointer">
            {t("pricing_system.platform.website")}{" "}
          </Label>
        </div>
        {/* Mobile App Checkbox */}
        <div
          className="flex w-full border-1 rounded-lg border-blue-600 gap-3 items-center p-3 cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={handlePlatformClick}
        >
          <Checkbox
            id="mobile"
            value="mobile"
            checked={platform.includes("mobile")}
            onCheckedChange={(checked) => {
              if (!checked && platform.length === 1 && platform.includes("mobile")) {
                // Auto-check web if user tries to uncheck mobile
                togglePlatform("web");
                toggleRole(
                  web?.id!,
                  web?.title!,
                  web?.description!,
                  web?.category!,
                  web?.cost!
                );
              }

              togglePlatform("mobile");
              toggleRole(
                mobile?.id!,
                mobile?.title!,
                mobile?.description!,
                mobile?.category!,
                mobile?.cost!
              );
              handlePlatformClick();
            }}

          />
          <Label htmlFor="mobile" className="cursor-pointer">
            {t("pricing_system.platform.mobile")}{" "}
          </Label>
        </div>
      </div>
    </div>
  );
};

export default Platform;
