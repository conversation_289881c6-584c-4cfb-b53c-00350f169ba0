"use client";
import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Role } from "@/app/[locale]/pricing/hooks/useFetchRoles";
import { useRoleStore } from "@/app/[locale]/pricing/store/roleStore";

interface TeamRoleProps {
  role: Role;
}

const TeamRole = ({ role }: TeamRoleProps) => {
  const { checkedRoles, updateRoleNumber } = useRoleStore();


  const roleData = checkedRoles.find((r) => r.id === role.id);
  const selectedNumber = roleData?.number || 1;



  return (
    <div className="flex justify-start gap-3 md:w-full w-[80%] mx-auto">
      <div className="flex border-1 w-full rounded-lg border-[#008FD4]  px-2 py-1  items-center justify-between">
        <div className="flex items-center w-full gap-2">

          <p className="md:text-md text-sm">{role.title}</p>
        </div>
        <Select
          disabled={[
            "management",
            "design",
          ].includes(role.category)}
          value={selectedNumber.toString()}
          onValueChange={(value) => {
            updateRoleNumber(role.id, Number(value));

          }}

        >
          <SelectTrigger
            className="w-12  px-3  border-1 border-[#008dd48f] rounded-lg "
          >
            <SelectValue placeholder="1" />
          </SelectTrigger>
          <SelectContent className="w-fit text-black">
            <SelectItem className="p-4" value="1">
              1
            </SelectItem>
            <SelectItem className="p-4" value="2">
              2
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

    </div>
  );
};

export default TeamRole;
