
"use client";

import Image from "next/image";
import Link from "next/link";
import React from "react";
import { useTranslation } from "react-i18next";

interface DomainCardProps {
  domainId: string;
  title: string;
  description: string;
  price?: number;
  icon: string;
  locale: string;
  disabled: boolean;
}

const DomainCard = ({
  domainId,
  title,
  description,
  price,
  icon,
  locale,
  disabled,
}: DomainCardProps) => {
  const { t } = useTranslation();

  const CardContent = (
    <div
      className={`w-fit lg:w-[400px] sm:w-[350px] text-[24px] flex justify-between flex-col bg-[#FBFEFF] aspect-square shadow-lg p-[32px] shadow-blue-100 rounded-xl
        ${locale === "ar" ? "zain-regular font-medium text-right" : "montserrat font-medium text-left"}
        ${disabled ? "cursor-not-allowed opacity-50" : "cursor-pointer"}
      `}
    >
      <div className={`flex flex-col ${locale === "ar" ? "items-end" : ""}`}>
        <Image
          src={`/icons/pricing/${icon}`}
          width={40}
          height={120}
          alt={"domain-icon"}
        />
        <h3 className="text-[#33748D] md:text-lg text-[22px] pt-12">
          {title}
        </h3>
        <p className="text-[#1F2126] sm:text-[14px] text-[13px]">
          {description}
        </p>
      </div>

      {

        disabled ?
          (
            <h1 className="font-semibold text-[#005171]">
              Coming Soon ...
            </h1>
          ) :
          price ? (
            <div>
              <p className="md:text-md text-[20px] text-[#4A626B]">
                {t("pricing_section.price")}
              </p>
              <p className="text-[#005171] md:text-[32px] text-[24px]">
                {price} {t("pricing_section.currency")}
              </p>
            </div>
          ) : (
            <span
              className="text-[#005171]"

            >
              {t("pricing_section.contact_us")}
            </span>
          )}
    </div>
  );

  if (disabled) {
    return <div>{CardContent}</div>;
  }

  return (
    <Link
      href={
        domainId === "contactUs"
          ? `/${locale}#contactUs`
          : `/${locale}/pricing/${domainId}/overview`
      }
    >
      {CardContent}
    </Link >
  );
};

export default DomainCard;
