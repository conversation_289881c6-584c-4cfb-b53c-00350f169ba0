"use client";

import React from "react";

const StageTime = ({
  time,
  isLoading,
  title,
  unit,
  hours
}: {
  time: number;
  isLoading: boolean;
  title: string;
  unit: string;
  hours: string;
}) => {
  return (
    <div className="flex flex-col my-2 justify-start text-sm text-[#005171]">
      <p className="font-semibold text-[#005171] text-md">{title}</p>
      {/* {!isLoading ? <p>{time} {hours}</p> : <p>wait ..</p>} */}
      <p>
        {
          time > 50 ? `${Math.floor(((time / (8 * 5)) * 10)/10) } ${unit}` :
            `0.5 ${unit}`
        }
      </p>
    </div>
  );
};

export default StageTime;
