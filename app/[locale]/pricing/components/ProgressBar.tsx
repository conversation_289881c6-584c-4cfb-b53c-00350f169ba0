"use client";
import React, { useEffect } from "react";
import { useStepStore } from "@/app/[locale]/pricing/store/useStepStore";
import { useTranslation } from "react-i18next";
const ProgressBar = ({ locale }: any) => {
  const { activeStep, steps } = useStepStore();
  const { t } = useTranslation();


  return (
    <div className="flex flex-col w-full gap-5 justify-center items-center ">
      <h2
        className={`md:text-2xl md:block hidden text-lg ${locale == "ar" ? "text-right" : "text-left"
          }`}
      >
        {t("pricing_system.title")}
      </h2>
      <p className="text-center md:block hidden">{t("pricing_system.description")}</p>
      <div className="flex items-start gap-1 md:w-[850px] sm:w-[400px] w-[300px]">
        {steps.map((step, index) => (
          <div
            key={step}
            className="flex flex-col  gap-2 items-start justify-start w-[20%]"
          >
            <div
              className={`h-[8px] rounded w-full ${index < activeStep
                ? "bg-[#005171]"
                : index === activeStep
                  ? "bg-[#A3E635]"
                  : "bg-gray-300"
                }`}
            />
            <div className="flex items-center gap-1">
              <div
                className={`aspect-square w-5 border-5 md:block hidden rounded-full ${index === activeStep
                  ? "border-[#A3E635]" // Active step
                  : index < activeStep
                    ? "border-[#005171]" // Completed step
                    : "border-2 border-gray-300" // Upcoming step
                  }`}
              />
              <span
                className={`sm:text-[12px] text-[10px] mt-1 ${index === activeStep
                  ? "text-black font-semibold"
                  : index < activeStep
                    ? "text-[#005171]"
                    : "text-gray-500"
                  }`}
              >
                {t(`pricing_system.steps.${index}`)}{" "}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProgressBar;
