
"use client";
import React, { useState, useRef, useEffect } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronDown, ChevronUp } from "lucide-react";
import Feature from "./Feature";
import { FeatureType } from "./FeaturesContainer";
import { useStepStore } from "@/app/[locale]/pricing/store/useStepStore";



const AdvancedFeatures = ({
  features,
  title,
  description,
  isRTL,
}: {
  features: FeatureType[];
  title: string;
  description: string;
  isRTL: boolean;
}) => {
  const { setActiveStep } = useStepStore();
  const ADVANCED_FEATURES_STEP_INDEX = 2;

  const handleFeatureClick = () => {
    setActiveStep(ADVANCED_FEATURES_STEP_INDEX);
  };

  const [showAll, setShowAll] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState<number | "auto">("auto");

  const visibleFeatures = showAll ? features : features.slice(0, 3);

  useEffect(() => {
    if (containerRef.current) {
      setHeight(containerRef.current.scrollHeight);
    }
  }, [showAll]);

  return (
    <div>
      <div className={`flex  ${isRTL ? 'flex-row-reverse' : 'flex-row'} justify-between `}>

        <div className={`${isRTL ? "text-right" : ""}`}>
          <div className={`flex justify-start items-center gap-2 ${isRTL ? "text-right flex-row-reverse" : ""}`}>
            <div className="h-5 w-[3px] bg-[#A3E635]" />
            <p className="text-[#005171] font-bold">{title}</p>
          </div>
          <p className="text-sm text-[#00517187]">{description}</p>
        </div>
        {features.length > 3 && (
          <div
            className={`mt-1 px-4 flex items-center justify-center ${isRTL ? "text-right" : "text-left"}`}
          >
            <button
              className=" px-3 py-1 flex items-center gap-2 transition-colors rounded-md text-[#005171] text-sm font-medium hover:text-[#003449]"
              onClick={() => setShowAll((prev) => !prev)}
            >
              {showAll ? (
                <>
                  <ChevronUp size={18} />

                </>
              ) : (
                <>
                  <ChevronDown size={18} />

                </>
              )}
            </button>
          </div>
        )}
      </div>

      <motion.div
        animate={{ height }}
        transition={{ duration: 0.5, ease: "easeInOut" }}
        className="overflow-hidden mt-4 px-4 flex flex-col gap-4"
      >
        <div ref={containerRef} className="flex flex-col h-auto gap-2">
          {visibleFeatures.map((feature) => (
            <div
              key={feature.title}
              onClick={handleFeatureClick}
              className="cursor-pointer hover:bg-gray-50 rounded-lg transition-colors "
            >
              <Feature feature={feature} />
            </div>
          ))}
        </div>
      </motion.div>

      {/* {features.length > 3 && (
        <div className={`mt-4 px-4 flex items-center justify-center ${isRTL ? "text-right" : "text-left"}`}>
          <button
            className=" px-3 py-1 flex items-center gap-2 transition-colors rounded-md text-[#005171] text-sm font-medium hover:text-[#003449]"
            onClick={() => {
              setHeight("auto");
              setShowAll((prev) => !prev);
            }}
          >
            {showAll ? (
              <>
                <ChevronUp size={18} />
                {isRTL ? "عرض أقل" : "Show less"}
              </>
            ) : (
              <>
                <ChevronDown size={18} />
                {isRTL ? "عرض المزيد" : "Show more"}
              </>
            )}
          </button>
        </div>
      )} */}
    </div>
  );
};

export default AdvancedFeatures;
