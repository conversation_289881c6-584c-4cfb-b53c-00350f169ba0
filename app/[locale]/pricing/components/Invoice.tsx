
"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import StageTime from "./StageTime";
import { Suspense, useEffect } from "react";
import { useCalculateTime } from "../hooks/useCalculateTime";
import { useRoleStore } from "../store/roleStore";
import { useFeatureStore } from "../store/featuresStore";
import { useFetchCost } from "../hooks/useFetchCost";
import Cost from "./Cost";
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useCreateClientRequest } from "../hooks/useCreateRequest"; // Import your custom hook
import toast from "react-hot-toast";
import { redirect } from "next/navigation";
import Link from "next/link";
import { generatePDF } from "../lib/generatePdf"; // Import your generatePDF function
import { title } from "process";

const Invoice = ({ locale }: any) => {
  const { checkedFeatures, platform } = useFeatureStore();
  const { checkedRoles } = useRoleStore();
  const { t } = useTranslation();
  const isRTL = locale == "ar";
  const {
    developmentTime,
    desginTime,
    analizingTime,
    devopsTime,
    QA_time,
    totalTime,
    isLoading,
  } = useCalculateTime(checkedRoles, checkedFeatures, platform);
  const {
    teamCost,
    basicFeaturesCost,
    extraFeaturesCost,
    addOnsCost,
    totalPrice,
    pending,
  } = useFetchCost();

  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const { mutate, isPending, isError, error, isSuccess } =
    useCreateClientRequest();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Prepare data for submission
    const submissionData = {
      name,
      email,
      phone,
      checkedFeatures: checkedFeatures.map((f: any) => ({
        id: f.id,
        base_price: f.base_price,
        category: f.category,
        type: f.type,
      })),
      checkedRoles: checkedRoles.map((r: any) => ({
        id: r.id,
        title: r.title,
        description: r.description,
        cost: r.cost,

        category: r.category,
        number: r.number,
      })),
      platform,
      total_time: totalTime!,
      total_price: totalPrice,
    };
    mutate(submissionData);
  };

  useEffect(() => {
    if (isSuccess) {
      toast.success("We received your request");
      setDialogOpen(false);
      setName("");
      setEmail("");
      setPhone("");
      redirect("/");
    }
  }, [isSuccess]);

  // Function to handle PDF generation
  const handleGeneratePDF = async () => {
    try {
      const pdfBlob = await generatePDF({
        features: checkedFeatures, // Assuming you have these variables
        roles: checkedRoles,
        totalTime: totalTime!,
        totalPrice: totalPrice,
      });

      // You might want to display the PDF or download it:
      const link = document.createElement('a');
      link.href = URL.createObjectURL(pdfBlob);
      link.download = 'invoice.pdf'; // Set the filename for download
      link.click();
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  };
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="invoice">
        <div className="flex flex-col gap-2">
          <div className="hidden md:flex flex-col gap-2">
            <div
              className={`flex gap-3 justify-start items-center text-xl mb-2 text-[#3CA3CC] ${isRTL ? "text-right flex-row-reverse" : "text-left"
                }`}
            >
              <div className="w-3 h-3 rounded-full bg-[#A3E635]" />
              <p>{t("pricing_system.invoice.price")}</p>
            </div>
            <Cost
              cost={basicFeaturesCost}
              title={t("pricing_system.invoice.costs.0")}
              loading={pending}
              isRTL={isRTL}
            />
            <Cost
              cost={extraFeaturesCost}
              title={t("pricing_system.invoice.costs.1")}
              loading={pending}
              isRTL={isRTL}
            />
            <Cost
              cost={addOnsCost}
              title={t("pricing_system.invoice.costs.2")}
              loading={pending}
              isRTL={isRTL}
            />
            {/* <Cost
              cost={teamCost}
              title={t("pricing_system.invoice.costs.3")}
              loading={pending}
              isRTL={isRTL}
            /> */}
            <hr />
          </div>
          <div
            className={`flex justify-between ${isRTL ? "text-right flex-row-reverse" : "text-left"
              }`}
          >
            <p>{t("pricing_system.invoice.total")}</p>
            <p className="text-lg">{totalPrice} AED</p>
          </div>
        </div>
        <div>
          <div className="hidden md:block">
            <div
              className={`flex gap-3 justify-start items-center text-xl mb-2 text-[#3CA3CC] ${isRTL ? "text-right flex-row-reverse" : "text-left"
                }`}
            >
              <div className="w-3 h-3 rounded-full bg-[#A3E635]" />
              <p>{t("pricing_system.invoice.time")}</p>
            </div>
            <div className="w-full h-[5px] mt-4 rounded-full bg-gradient-to-r from-[#71B331FF] to-[#005171FF] " />
            <div className="flex justify-between">
              {/* <StageTime
                time={meetingTime!}
                isLoading={false}
                unit={t("pricing_system.invoice.time_unit")}
                hours={t("pricing_system.invoice.hours")}
                title={t("pricing_system.invoice.stages.0")}
              /> */}
              <StageTime
                time={analizingTime!}
                isLoading={false}
                unit={t("pricing_system.invoice.time_unit")}
                hours={t("pricing_system.invoice.hours")}
                title={t("pricing_system.invoice.stages.0")}
              />
              <StageTime
                time={desginTime!}
                isLoading={isLoading}
                unit={t("pricing_system.invoice.time_unit")}
                hours={t("pricing_system.invoice.hours")}
                title={t("pricing_system.invoice.stages.1")}
              />
              <StageTime
                time={developmentTime!}
                isLoading={isLoading}
                unit={t("pricing_system.invoice.time_unit")}
                hours={t("pricing_system.invoice.hours")}
                title={t("pricing_system.invoice.stages.2")}
              />
              <StageTime
                time={devopsTime! + QA_time!}
                isLoading={isLoading}
                unit={t("pricing_system.invoice.time_unit")}
                hours={t("pricing_system.invoice.hours")}
                title={t("pricing_system.invoice.stages.3")}
              />
            </div>
          </div>
          <div
            className={`flex mt-5 justify-between gap-5 w-full text-[#005171] ${isRTL ? "text-right flex-row-reverse" : "text-left"
              }`}
          >
            <p>{t("pricing_system.invoice.total_time")}</p>
            <p>
              {Math.floor(((totalTime! / (8 * 5)) * 10) / 10)}{" "}
              {t("pricing_system.invoice.time_unit")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3 mt-5">
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger className="text-white  p-2 rounded-lg w-full bg-[#005171] hover:bg-[#216d8b]">
              {t("pricing_system.invoice.submit")}
            </DialogTrigger>
            <DialogContent className="bg-white">
              <DialogHeader
                className={`${isRTL ? "text-right " : "text-left"}`}
              >
                <DialogTitle
                  className={`${isRTL ? "text-right pr-5 " : "text-left"}`}
                >
                  {t("pricing_system.invoice.submit_title")}
                </DialogTitle>
                <DialogDescription
                  className={`${isRTL ? "text-right pr-5 " : "text-left"}`}
                >
                  {t("pricing_system.invoice.submit_description")}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="flex flex-col gap-3">
                <div
                  className={`flex flex-col gap-2 ${isRTL ? "text-right pr-5" : "text-left"
                    }`}
                >
                  <Label>{t("form.name")}</Label>
                  <Input
                    className={`${isRTL ? "text-right pr-5" : "text-left"}`}
                    placeholder={t("form.name_placeholder")}
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required
                  />
                </div>
                <div
                  className={`flex flex-col gap-2 ${isRTL ? "text-right pr-5" : "text-left"
                    }`}
                >
                  <Label>{t("form.email")}</Label>
                  <Input
                    className={`${isRTL ? "text-right pr-5" : "text-left"}`}
                    type="email"
                    placeholder={t("form.email_placeholder")}
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>
                <div
                  className={`flex flex-col gap-2 ${isRTL ? "text-right pr-5" : "text-left"
                    }`}
                >
                  <Label>{t("form.phone")}</Label>
                  <Input
                    className={`${isRTL ? "text-right pr-5" : "text-left"}`}
                    type="text"
                    placeholder="+971 "
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    required
                  />
                </div>
                <DialogFooter
                  className={`flex gap-3 mt-4 ${isRTL ? "pr-5" : ""}`}
                >
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setDialogOpen(false)}
                    disabled={isPending}
                  >
                    {t("form.cancel")}
                  </Button>
                  <Button
                    type="submit"
                    disabled={isPending}
                    className="bg-[#005171] hover:bg-[#216d8b]"
                  >
                    {isPending ? t("form.submitting") : t("form.submit")}
                  </Button>
                </DialogFooter>
              </form>

              {isError && (
                <div className="mt-4 text-red-500">
                  {error?.message || t("form.submit_error")}
                </div>
              )}
            </DialogContent>
          </Dialog>
          {/* <Link href={"/requests"} className="w-full p-2 text-center text-white rounded-lg text-md  bg-[#005171]">{t("form.browse")}</Link> */}
        </div>


      </div>
    </Suspense>
  );
};

export default Invoice;
