
"use client";

import React, { useEffect, useState, useRef } from "react";
import FeatureItem from "./Feature";
import { FeatureType } from "./FeaturesContainer";
import { useFeatureStore } from "@/app/[locale]/pricing/store/featuresStore";
import { motion } from "framer-motion";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useStepStore } from "../store/useStepStore";

const BasicFeatures = ({
  features,
  title,
  description,
  isRTL,
}: {
  features: FeatureType[];
  title: string;
  description: string;
  isRTL: boolean;
}) => {
  const { setFeatures } = useFeatureStore();
  const [featuresToggled, setFeaturesToggled] = useState(false);
  const [showAll, setShowAll] = useState(false);
  const [containerHeight, setContainerHeight] = useState<number>(0);

  const innerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (features?.length > 0 && !featuresToggled) {
      setFeatures(features);
      setFeaturesToggled(true);
    }
  }, [features, featuresToggled]);
  const { setActiveStep } = useStepStore()
  // Recalculate height on content change
  useEffect(() => {
    if (innerRef.current) {
      setContainerHeight(innerRef.current.scrollHeight);
    }
  }, [showAll, features]);

  const visibleFeatures = showAll ? features : features.slice(0, 0);

  const BASIC_FEATURES_STEP_INDEX = 1
  const handleFeatureClick = () => {
    setActiveStep(BASIC_FEATURES_STEP_INDEX);
  };
  return (
    <div>
      <div className={`flex  ${isRTL? 'flex-row-reverse' : 'flex-row'} justify-between `}>

        <div className={`${isRTL ? "text-right flex-row-reverse" : ""}`}>
          <div
            className={`flex justify-start items-center gap-2 ${isRTL ? "text-right flex-row-reverse" : ""
              }`}
          >
            <div className="h-5 w-[3px] bg-[#A3E635]" />
            <p className="text-[#005171] font-bold">{title}</p>
          </div>
          <p className="text-sm text-[#00517187]">{description}</p>
        </div>
        {features.length > 0 && (
          <div
            className={`mt-1 px-4 flex items-center justify-center ${isRTL ? "text-right" : "text-left"}`}
          >
            <button
              className=" px-3 py-1 flex items-center gap-2 transition-colors rounded-md text-[#005171] text-sm font-medium hover:text-[#003449]"
              onClick={() => setShowAll((prev) => !prev)}
            >
              {showAll ? (
                <>
                  <ChevronUp size={18} />
                  
                </>
              ) : (
                <>
                  <ChevronDown size={18} />
                  
                </>
              )}
            </button>
          </div>
        )}
      </div>

      <motion.div
        animate={{ height: containerHeight }}
        transition={{ duration: 0.5, ease: "easeInOut" }}
        className="overflow-hidden mt-4 px-4"
      >
        <div ref={innerRef} className="flex flex-col gap-2" onClick={handleFeatureClick}>
          {visibleFeatures.map((feature: FeatureType) => (
            <FeatureItem
              key={feature.id}
              feature={feature}
            />
          ))}
        </div>
      </motion.div>

      {/* {features.length > 0 && (
        <div
          className={`mt-1 px-4 flex items-center justify-center ${isRTL ? "text-right" : "text-left"}`}
        >
          <button
            className=" px-3 py-1 flex items-center gap-2 transition-colors rounded-md text-[#005171] text-sm font-medium hover:text-[#003449]"
            onClick={() => setShowAll((prev) => !prev)}
          >
            {showAll ? (
              <>
                <ChevronUp size={18} />
                {isRTL ? "إخفاء" : "Hide"}
              </>
            ) : (
              <>
                <ChevronDown size={18} />
                {isRTL ? "عرض" : "Show"}
              </>
            )}
          </button>
        </div>
      )} */}
    </div>
  );
};

export default BasicFeatures;
