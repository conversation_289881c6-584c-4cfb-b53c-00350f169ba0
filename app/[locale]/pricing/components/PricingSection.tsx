
import React from "react";
import DomainsContainer from "./DomainsContainer";

interface PricingSectionProps {
  t: (key: string) => string;
  locale: string;
}
const PricingSection = ({ t, locale }: PricingSectionProps) => {
  return (
    <main className="px-[40px]" id="pricing">
      <div className="w-full flex justify-center">
        {/* Text */}
        <div className="text-center" dir={locale === "ar" ? "rtl" : ""}>
          <h2
            className={`font-medium text-[#005171] ${locale === "ar"
                ? "zain-regular text-[2.6rem] lg:text-[3.6rem]"
                : "outfit text-[2.6rem] lg:text-[3.6rem]"
              }`}
          >
            {t("pricing_section.title")}
          </h2>
          <p
            className={`text-[#005171] px-[2rem] lg:px-[7rem] ${locale === "ar"
                ? "zain-regular text-[1.4rem] xl:text-[1.5rem]"
                : "outfit text-[1.4rem] xl:text-[1.5rem]"
              }`}
            style={{ textShadow: "0 0 8px rgba(255, 255, 255, 0.4)" }}
          >
            {t("pricing_section.subtitle")}
          </p>
        </div>
      </div>

      <div className="my-10">
        <DomainsContainer locale={locale} />
      </div>
    </main>
  );
};

export default PricingSection;
