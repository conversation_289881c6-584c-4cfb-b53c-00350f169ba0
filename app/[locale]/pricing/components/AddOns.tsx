"use client";
import React, { useEffect, useRef, useState } from "react";
import Feature from "./Feature";
import { FeatureType } from "./FeaturesContainer";
import { useStepStore } from "@/app/[locale]/pricing/store/useStepStore";
import { motion } from "framer-motion";
import { ChevronDown, ChevronUp } from "lucide-react";
import { HeartHandshake, CircleDollarSign } from "lucide-react";
const AddOns = ({
  features,
  title,
  description,
  support,
  marketing,
  isRTL,
}: {
  features: FeatureType[];
  title: string;
  description: string;
  support: string;
  marketing: string;
  isRTL: boolean;
}) => {
  const { setActiveStep } = useStepStore();

  const STEP_INDEXES = {
    SUPPORT: 3,
    MARKETING: 4,
  };

  const handleFeatureClick = (category: string) => {
    if (category === "Support") setActiveStep(STEP_INDEXES.SUPPORT);
    if (category === "Marketing") setActiveStep(STEP_INDEXES.MARKETING);
  };

  const supportFeatures = features.filter((f) => f.category === "Support");
  const marketingFeatures = features.filter((f) => f.category === "Marketing");

  // Support toggling
  const [showAllSupport, setShowAllSupport] = useState(false);
  const supportRef = useRef<HTMLDivElement>(null);
  const [supportHeight, setSupportHeight] = useState<number | "auto">("auto");

  // Marketing toggling
  const [showAllMarketing, setShowAllMarketing] = useState(false);
  const marketingRef = useRef<HTMLDivElement>(null);
  const [marketingHeight, setMarketingHeight] = useState<number | "auto">("auto");

  // Visible items
  const visibleSupport = showAllSupport ? supportFeatures : supportFeatures.slice(0, 3);
  const visibleMarketing = showAllMarketing ? marketingFeatures : marketingFeatures.slice(0, 3);

  // Recalculate heights on toggle
  useEffect(() => {
    if (supportRef.current) setSupportHeight(supportRef.current.scrollHeight);
  }, [showAllSupport, supportFeatures]);

  useEffect(() => {
    if (marketingRef.current) setMarketingHeight(marketingRef.current.scrollHeight);
  }, [showAllMarketing, marketingFeatures]);

  return (
    <div>
      <div className={`${isRTL ? "text-right" : ""}`}>
        <div className={`flex items-center gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="h-5 w-[3px] bg-[#A3E635]" />
          <p className="text-[#005171] font-bold">{title}</p>
        </div>
        <p className="text-sm text-[#00517187]">{description}</p>
      </div>

      {/* Support Section */}
      <div className="flex flex-col gap-4 mt-6">
      <div className={`flex  ${isRTL? 'flex-row-reverse' : 'flex-row'} justify-between `}>

          <div className={`flex items-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
            <HeartHandshake className="text-[#92cf2f]" />
            <span className="text-[12px] mt-1 text-[#005171]">{support}</span>
          </div>
          {supportFeatures.length > 3 && (
            <div className={`mt-2 px-4 flex items-center justify-center`}>
              <button
                onClick={() => {
                  setSupportHeight("auto");
                  setShowAllSupport((prev) => !prev);
                }}
                className="px-3 py-1 flex items-center gap-2 transition-colors rounded-md text-[#005171] text-sm font-medium hover:text-[#003449]"
              >
                {showAllSupport ? (
                  <>
                    <ChevronUp size={18} />
                  </>
                ) : (
                  <>
                    <ChevronDown size={18} />
                  </>
                )}
              </button>
            </div>
          )}
        </div>

        <motion.div
          animate={{ height: supportHeight }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
          className="overflow-hidden px-4"
        >
          <div ref={supportRef} className="flex flex-col gap-4">
            {visibleSupport.map((feature) => (
              <div
                key={feature.title}
                onClick={() => handleFeatureClick(feature.category)}
                className="cursor-pointer hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Feature feature={feature} />
              </div>
            ))}
          </div>
        </motion.div>


      </div>

      {/* Marketing Section */}
      <div className="flex flex-col gap-4 mt-6">
      <div className={`flex  ${isRTL? 'flex-row-reverse' : 'flex-row'} justify-between `}>

          <div className={`flex items-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
            <CircleDollarSign className="text-[#92cf2f]" />
            <span className="text-[12px] mt-1 text-[#005171]">{marketing}</span>
          </div>
          {marketingFeatures.length > 3 && (
            <div className={`mt-2 px-4 flex h-auto items-center justify-center`}>
              <button
                onClick={() => {
                  setMarketingHeight("auto");
                  setShowAllMarketing((prev) => !prev);
                }}
                className="px-3 py-1 flex items-center gap-2 transition-colors rounded-md text-[#005171] text-sm font-medium hover:text-[#003449]"
              >
                {showAllMarketing ? (
                  <>
                    <ChevronUp size={18} />

                  </>
                ) : (
                  <>
                    <ChevronDown size={18} />
                  </>
                )}
              </button>
            </div>
          )}
        </div>

        <motion.div
          animate={{ height: marketingHeight }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
          className="overflow-hidden px-4"
        >
          <div ref={marketingRef} className="flex flex-col gap-4">
            {visibleMarketing.map((feature) => (
              <div
                key={feature.title}
                onClick={() => handleFeatureClick(feature.category)}
                className="cursor-pointer hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Feature feature={feature} />
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default AddOns;
