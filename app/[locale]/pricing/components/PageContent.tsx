"use client";
import React, { useEffect } from "react";
import ProgressBar from "./ProgressBar";
import Invoice from "./Invoice";
import RequiredTeam from "./RequiredTeam";
import FeaturesContainer from "./FeaturesContainer";
import Platform from "./Platform";
import NavBar from "../../Components/NavBar";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet"
import { Button } from "@/components/ui/button";
import { ChevronUp } from "lucide-react";
import { useFetchRoles } from "../hooks/useFetchRoles";
import { useRouter } from "next/navigation";
interface PageContentProps {
  locale: string;
  id: string;
}

const PageContent: React.FC<PageContentProps> = ({ locale, id }) => {
  const { data, isLoading } = useFetchRoles(locale);
  const router = useRouter()

  useEffect(() => {
    router.refresh()
  }, [locale])

  return (
    <main
      className={`w-full m-auto flex flex-col items-center relative overflow-hidden h-fit max-w-[1700px] ${locale === "ar" ? "zain-regular" : "outfit"
        }`}
    >
      <div className="w-full bg-white  fixed z-50 m-auto ">
        <NavBar locale={locale} />
        <div className="md:p-32 pt-[100px] md:pb-10 pb-5 w-full border-b-1 fixed z-20 bg-white">
          <ProgressBar locale={locale} />
        </div>
      </div>
      <div className="flex items-start w-full    relative">
        <div className=" page-content">
          <Invoice locale={locale} />
          <RequiredTeam locale={locale} data={data} isLoading={isLoading} />
        </div>
        <Sheet >
          <SheetTrigger asChild className="md:hidden absolute bottom-8 right-[5%]">
            <Button variant="secondary" className="bg-[#005171] hover:bg-[#1b6785] " disabled={isLoading}><ChevronUp size={18} color="white" /></Button>
          </SheetTrigger>
          <SheetContent side={'bottom'} className="bg-white">

            <div className=" md:w-full 2md:hidden md:flex justify-between flex-col  md:flex-row">
              <Invoice locale={locale} />
              <RequiredTeam locale={locale} data={data} isLoading={isLoading} />
            </div>

          </SheetContent>
        </Sheet>
        <div className="flex md:h-[620px] h-[550px]  mb-[10px] overflow-y-scroll flex-col gap-8 px-[10px] w-[98%] pb-5 md:mt-[20rem] mt-[200px]">
          <Platform locale={locale} />
          <FeaturesContainer locale={locale} domainId={id} />
        </div>
      </div>

    </main>
  );
};

export default PageContent;
