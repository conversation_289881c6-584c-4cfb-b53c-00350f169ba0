
"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { FeatureType } from "./FeaturesContainer";
import { useFeatureStore } from "@/app/[locale]/pricing/store/featuresStore";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Label } from "@/components/ui/label";
import { useRoleStore } from "../store/roleStore";


const FeatureItem = ({
  feature,
}: {
  feature: FeatureType;
}) => {
  const { toggleFeature, checkedFeatures } = useFeatureStore();
  const { toggleRole, checkedRoles, roles } = useRoleStore();

  const checked = checkedFeatures.some((f) => f.id === feature.id);
  const isDisabled = feature.type === "Basic";

  const handleFeatureToggle = () => {
    toggleFeature(feature);

    if (feature.role_category === "content" || feature.role_category === "marketing") {
      const relatedRole = roles.find((role) => role.category === feature.role_category);
      const isAlreadyChecked = checkedRoles.some(
        (r) => r.category === feature.role_category
      );

      const isFeatureCurrentlyChecked = checkedFeatures.some(
        (f) => f.id === feature.id
      );

      if (relatedRole) {
        if (!isFeatureCurrentlyChecked && !isAlreadyChecked) {
          toggleRole(
            relatedRole.id,
            relatedRole.title,
            relatedRole.description,
            relatedRole.category,
            relatedRole.cost
          );
        } else if (
          isFeatureCurrentlyChecked &&
          ["marketing", "content"].includes(feature.role_category)
        ) {
          toggleRole(
            relatedRole.id,
            relatedRole.title,
            relatedRole.description,
            relatedRole.category,
            relatedRole.cost
          );
        }
      }
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={`flex justify-between border-1 rounded-lg border-[#008dd4be] p-2 gap-14 items-center transition-colors ${isDisabled ? "cursor-not-allowed p-3" : "cursor-pointer hover:bg-gray-100"
              }`}
            onClick={() => {
              if (!isDisabled) handleFeatureToggle();
            }}
          >
            <div className="flex items-center gap-3">
              {feature.type !== "Basic" && (
                <Checkbox
                className="w-4 h-4"
                  checked={checked}
                  id={feature.id}
                  onClick={(e) => e.stopPropagation()}
                  onCheckedChange={() => handleFeatureToggle()}
                  disabled={isDisabled}
                />
              )}
              <Label htmlFor={feature.id} className="cursor-pointer">
                {feature?.title}
              </Label>
            </div>
            {feature.type !== "Basic" && <p>{feature.base_price} AED</p>}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p className="md:text-lg text-wrap max-w-[500px] md:w-full w-[300px] text-md ">{feature.description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default FeatureItem;