"use client";

import useFetchDomains from "@/app/[locale]/pricing/hooks/useFetchDomains";
import React from "react";
import DomainCard from "./DomainCard";
import Link from "next/link";
import { useTranslation } from "react-i18next";
interface ContainerProps {
  locale: string;
}
interface Domain {
  id: string;
  slug: string;
  description: string;
  starting_price: number;
  title: string;
  icon: string,
  disabled: boolean
}
const DomainsContainer = ({ locale }: ContainerProps) => {
  const { data, isLoading } = useFetchDomains(locale);
  const { t } = useTranslation()
  return (
    <div className="flex flex-wrap px-10  w-full items-center  justify-center gap-[50px]">
      {!isLoading &&
        data?.map((domain: Domain) => {
          return (
            <DomainCard
              key={domain.id}
              domainId={domain.slug}
              title={domain.title}

              description={domain.description}
              price={domain.starting_price}
              icon={domain.icon}
              locale={locale}
              disabled={domain.disabled}
            />
          );
        })}


      <DomainCard
        domainId={'contactUs'}
        title={t("pricing_section.custom_title")}
        description={t("pricing_section.custom_description")}
        icon={'custom.svg'}
        locale={locale}
        disabled={false}
      />
    </div>
  );
};

export default DomainsContainer;

