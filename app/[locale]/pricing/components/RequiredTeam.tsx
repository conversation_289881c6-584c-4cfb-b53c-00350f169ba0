
"use client";
import React, { useEffect, useRef, useState } from "react";
import TeamRole from "./TeamRole";
import {
  Role,
} from "@/app/[locale]/pricing/hooks/useFetchRoles";
import { useStepStore } from "@/app/[locale]/pricing/store/useStepStore";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useRoleStore } from "../store/roleStore";
import Spinner from "./Spinner";

const RequiredTeam = ({ locale, data, isLoading }: { locale: string, data: any, isLoading: boolean }) => {
  const { setActiveStep } = useStepStore();
  const { t } = useTranslation();
  const isRTL = locale === "ar";
  const { checkedRoles, roles, toggleRole, setRoles } = useRoleStore()
  const REQUIRED_TEAM_STEP_INDEX = 1;
  const handleRoleClick = () => {
    setActiveStep(REQUIRED_TEAM_STEP_INDEX);
  };
  ;

  const [showAll, setShowAll] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState<number | "auto">("auto");

  const visibleRoles = showAll ? checkedRoles : checkedRoles?.slice(0, 3);

  useEffect(() => {
    if (containerRef.current) {
      setHeight(containerRef.current.scrollHeight);
    }
  }, [showAll, checkedRoles]);


  return (
    <div className="mb-8  w-full">
      <div className={`${isRTL ? "text-right" : ""}`}>
        <div
          className={`flex justify-start items-center gap-2 ${isRTL ? "text-right flex-row-reverse" : ""
            }`}
        >
          <div className="h-5 w-[3px] bg-[#A3E635]" />
          <p className="text-[#005171] font-bold text-lg">
            {t("pricing_system.team.title")}
          </p>
        </div>
        <p className="text-sm text-[#00517187] ">
          {t("pricing_system.team.description")}
        </p>
      </div>


      <>
        <motion.div
          animate={{ height }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
          className="overflow-hidden mt-5 px-4"
        >
          <div ref={containerRef} className="flex flex-col gap-2">
            {visibleRoles?.map((role: Role) => (
              <div
                key={role.id}
                onClick={handleRoleClick}
                className="cursor-pointer hover:bg-gray-50 rounded-lg transition-colors"
              >
                <TeamRole role={role} key={role.id} />
              </div>
            ))}
          </div>
        </motion.div>
        {
          isLoading && (<div className="flex my-20 justify-center items-center"><Spinner /></div>)
        }
        {!isLoading && data && data.length > 3 && (
          <div className="mt-2  px-4 flex items-center justify-center">
            <button
              onClick={() => {
                setHeight("auto");
                setShowAll((prev) => !prev);
              }}
              className="px-3 py-1 flex items-center gap-2 transition-colors rounded-md text-[#005171] text-sm font-medium hover:text-[#003449]"
            >
              {showAll ? (
                <>
                  <ChevronUp size={18} />
                  {isRTL ? "عرض أقل" : "Show less"}
                </>
              ) : (
                <>
                  <ChevronDown size={18} />
                  {isRTL ? "عرض المزيد" : "Show more"}
                </>
              )}
            </button>
          </div>
        )}
      </>

    </div>
  );
};

export default RequiredTeam;
