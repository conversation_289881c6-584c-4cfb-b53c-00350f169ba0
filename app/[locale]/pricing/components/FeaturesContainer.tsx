"use client";
import React from "react";
import BasicFeatures from "./BasicFeatures";
import AdvancedFeatures from "./AdvancedFeatures";
import { useFetchFeatures } from "../hooks/useFetchFeatures";
import AddOns from "./AddOns";
import { useTranslation } from "react-i18next";
import Spinner from "./Spinner";

export interface FeatureType {
  id: string;
  title: string;
  type: "Basic" | "Optional" | "Add_On";
  description: string,
  backend_time: number;
  web_time: number;
  mobile_time: number;
  category: string;
  role_category: string;
  QA_time: number;
  design_time: number;
  devops_time: number;
  base_price: number;
}

const FeaturesContainer = ({ locale, domainId }: any) => {
  const { data, isLoading } = useFetchFeatures(domainId, locale);
  const { t } = useTranslation();
  if (isLoading) return <div className="flex my-20 justify-center items-center"><Spinner /></div>;
  if (!data || !data.features) return <div>No features found</div>;

  const basicFeatures: FeatureType[] = data?.features.filter(
    (feature: any) => feature.type === "Basic"
  );
  const optionalFeatures: FeatureType[] = data?.features.filter(
    (feature: any) => feature.type === "Optional"
  );
  const addOnFeatures: FeatureType[] = data?.features.filter(
    (feature: any) => feature.type === "Add_On"
  );
  const isRTL = locale == "ar";
  return (
    <div className="flex flex-col gap-5">

      {(!isLoading && data.features) && (
        <>
          <div>
            <BasicFeatures
              features={basicFeatures}
              title={t("pricing_system.basic.title")}
              description={t("pricing_system.basic.description")}
              isRTL={isRTL}
            />
          </div>
          <div>
            <AdvancedFeatures
              title={t("pricing_system.advanced.title")}
              description={t("pricing_system.advanced.description")}
              features={optionalFeatures}
              isRTL={isRTL}
            />
          </div>
          <div>
            <AddOns
              features={addOnFeatures}
              title={t("pricing_system.addons.title")}
              description={t("pricing_system.addons.description")}
              support={t("pricing_system.addons.support")}
              marketing={t("pricing_system.addons.marketing")}
              isRTL={isRTL}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default FeaturesContainer;
