import { useQuery } from "@tanstack/react-query";
import axiosInstance from "@/lib/axiosInstance";
import { useFeatureStore } from "@/app/[locale]/pricing/store/featuresStore";
import { useEffect } from "react";
const fetchFeatures = async (slug: string, locale: string) => {
  try {
    const { data } = await axiosInstance.get(`/api/features`, {
      headers: {
        "Content-Type": "application/json",
        locale: locale,
      },
      params: {
        slug: slug,
      },
    });

    return data;
  } catch (error) {
    console.log(error);
  }
};

export const useFetchFeatures = (slug: string, locale: string) => {
  const { setFeatures } = useFeatureStore();
  const { data, isLoading } = useQuery({
    queryKey: ["features", slug, locale],
    queryFn: () => fetchFeatures(slug, locale),
    retry: 2,
    retryDelay: attempt => Math.min(1000 * 2 ** attempt, 30000),
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 10,
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (data?.data) {
      setFeatures(data.data.features);
    }
  }, [data, setFeatures]);

  return {
    data: data?.data,
    isLoading,
  };
};
