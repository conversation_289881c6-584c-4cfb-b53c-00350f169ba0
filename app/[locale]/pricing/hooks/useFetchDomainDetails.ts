import { useQuery } from "@tanstack/react-query";

interface DomainDetails {
  id: string;
  title: string;
  description: string;
  preview_title: string;
  preview_description: string;
  starting_price: number;
  icon: string;
  screenshots: string[];
  slug: string;
  disabled: boolean;
}

interface Feature {
  id: string;
  title: string;
  description: string;
  type: "Basic" | "Optional" | "Add_On";
  category: string;
  role_category: string;
  base_price: number;
  backend_time: number;
  design_time: number;
  web_time: number;
  QA_time: number;
  devops_time: number;
  mobile_time: number;
}

interface DomainDetailsResponse {
  domain: DomainDetails;
  features: Feature[];
}

const fetchDomainDetails = async (slug: string, locale: string): Promise<DomainDetailsResponse> => {
  // Fetch domain details
  const domainResponse = await fetch("/api/domains", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      locale: locale,
    },
  });

  if (!domainResponse.ok) {
    throw new Error("Failed to fetch domains");
  }

  const domainData = await domainResponse.json();
  const domain = domainData.data.domains.find((d: DomainDetails) => d.slug === slug);

  if (!domain) {
    throw new Error("Domain not found");
  }

  // Fetch features for this domain
  const featuresResponse = await fetch(`/api/features?slug=${slug}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      locale: locale,
    },
  });

  if (!featuresResponse.ok) {
    throw new Error("Failed to fetch features");
  }

  const featuresData = await featuresResponse.json();

  return {
    domain,
    features: featuresData.data.features || [],
  };
};

export const useFetchDomainDetails = (slug: string, locale: string) => {
  const { data, error, isLoading } = useQuery({
    queryKey: ["domainDetails", slug, locale],
    queryFn: () => fetchDomainDetails(slug, locale),
    retry: 2,
    retryDelay: attempt => Math.min(1000 * 2 ** attempt, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
    refetchOnWindowFocus: false,
    enabled: !!slug, // Only run query if slug exists
  });

  return {
    domain: data?.domain,
    features: data?.features || [],
    error,
    isLoading,
  };
};

export type { DomainDetails, Feature, DomainDetailsResponse };
