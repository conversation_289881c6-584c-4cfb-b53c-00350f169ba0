import { useQuery } from "@tanstack/react-query";

const fetchDomains = async (locale: string) => {
  const response = await fetch("/api/domains", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      locale: locale, // Sending locale here
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch domains");
  }

  return response.json();
};

const useFetchDomains = (locale: string) => {
  const { data, error, isLoading } = useQuery({
    queryKey: ["domains", locale],
    queryFn: () => fetchDomains(locale),
    retry: 3,           // number of retry attempts (default is 3)
    retryDelay: attempt => Math.min(1000 * 2 ** attempt, 30000),
  });
  console.log(data);

  return {
    data: data?.data.domains,
    error,
    isLoading,
  };
};

export default useFetchDomains;
