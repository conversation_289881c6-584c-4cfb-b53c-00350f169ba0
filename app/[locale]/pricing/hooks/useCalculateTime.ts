import { calculateTime } from "../lib/calculateTime";
import { CheckedRole } from "@/app/[locale]/pricing/store/roleStore";
import { CheckedFeature } from "@/app/[locale]/pricing/store/featuresStore";
import { useQuery } from "@tanstack/react-query";

export const fetchTime = async (
  checkedRoles: CheckedRole[],
  checkedFeatures: CheckedFeature[],
  platform: string[]
) => {
  try {
    const data = calculateTime(checkedRoles, checkedFeatures, platform);
    return data;
  } catch (error) {
    console.error("Error calculating time:", error);
    throw error;
  }
};

export const useCalculateTime = (
  checkedRoles: CheckedRole[],
  checkedFeatures: CheckedFeature[],
  platform: string[]
) => {
  const { data, isLoading } = useQuery({
    queryKey: ["time", checkedRoles, checkedFeatures, platform],
    queryFn: () => fetchTime(checkedRoles, checkedFeatures, platform),
  });

  return {
    analizingTime: data?.analizingTime,
    developmentTime: data?.developmentTime,
    desginTime: data?.designTime,
    devopsTime: data?.devopsTime,
    QA_time: data?.QA_time,
    totalTime: data?.totalTime,
    isLoading,
  };
};
