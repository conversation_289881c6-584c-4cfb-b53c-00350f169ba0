import { useQuery } from "@tanstack/react-query";
import { calculateTeamCost } from "../lib/calculateTeamCost";
import { CheckedRole, useRoleStore } from "@/app/[locale]/pricing/store/roleStore";
import { CheckedFeature, useFeatureStore } from "@/app/[locale]/pricing/store/featuresStore";
export const fetchCost = async (
  checkedRoles: CheckedRole[],
  checkedFeatures: CheckedFeature[],
  platform: string[]
) => {
  try {
    const {
      teamCost,
      basicFeaturesCost,
      extraFeaturesCost,
      addOnsCost,
      totalPrice,
    } = calculateTeamCost(checkedRoles, checkedFeatures, platform);
    return {
      teamCost,
      basicFeaturesCost,
      extraFeaturesCost,
      addOnsCost,
      totalPrice,
    };
  } catch (error) {
    console.log(error);
  }
};

export const useFetchCost = () => {
  const checkedRoles = useRoleStore((state) => state.checkedRoles);
  const checkedFeatures = useFeatureStore((state) => state.checkedFeatures);
  const platform = useFeatureStore((state) => state.platform);
  const { data, isLoading } = useQuery({
    queryKey: ["cost", checkedRoles, checkedFeatures, platform],
    queryFn: () => fetchCost(checkedRoles, checkedFeatures, platform),
    enabled: checkedRoles.length > 0,
    
  });

  return {
    teamCost: data?.teamCost ?? 0,
    basicFeaturesCost: data?.basicFeaturesCost ?? 0,
    extraFeaturesCost: data?.extraFeaturesCost ?? 0,
    addOnsCost: data?.addOnsCost ?? 0,
    totalPrice: data?.totalPrice ?? 0,
    pending: isLoading,
  };
};
