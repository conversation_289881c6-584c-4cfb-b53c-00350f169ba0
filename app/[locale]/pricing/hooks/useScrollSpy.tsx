"use client";
import { useEffect, useState, RefObject } from 'react';

const useScrollSpy = (sectionsRef: RefObject<HTMLElement>[]) => {
  const [activeStep, setActiveStep] = useState(0);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = sectionsRef.findIndex(
              (ref) => ref.current === entry.target
            );
            if (index !== -1) {
              setActiveStep(index);
            }
          }
        });
      },
      { threshold: 0.5 }
    );

    sectionsRef.forEach((ref) => {
      if (ref.current) {
        observer.observe(ref.current);
      }
    });

    return () => {
      sectionsRef.forEach((ref) => {
        if (ref.current) {
          observer.unobserve(ref.current);
        }
      });
    };
  }, [sectionsRef]);

  return activeStep;
};

export default useScrollSpy;