// hooks/useCreateClientRequest.ts
import { useMutation } from "@tanstack/react-query";
import { ApiError } from "@/lib/ApiError";
import { CheckedRole } from "@/app/[locale]/pricing/store/roleStore";


interface RequestFeature{
  id: string;
  base_price: number;
  category: string;
  type: "Basic" | "Optional" | "Add_On";
}
interface CreateRequestParams {
  name: string;
  email: string;
  phone: string;
  checkedFeatures: RequestFeature[];
  checkedRoles: CheckedRole[];
  platform: string[];
  total_time: number;
  total_price: number;
}

interface CreateRequestResponse {
  client: {
    _id: string;
    name: string;
    email: string;
    phone: string;
  };
  request: {
    _id: string;
    total_time: number;
    total_price: number;
    platforms: string[];
  };
  features: Array<{
    _id: string;
    feature_id: string;
    final_price: number;
  }>;
  roles: Array<{
    _id: string;
    role_id: string;
    required_members: number;
  }>;
}

export const useCreateClientRequest = () => {
  return useMutation<CreateRequestResponse, <PERSON>pi<PERSON><PERSON><PERSON>, CreateRequestParams>({
    mutationFn: async (data) => {

      const response = await fetch("/api/clients", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new ApiError(
          response.status,
          errorData.message || "Failed to create request"
        );
      }

      return response.json();
    },
    // Optional: Add side effects
    onSuccess: (data) => {
      console.log("Request created successfully:", data);
    },
    onError: (error) => {
      console.error("Error creating request:", error.message);
    },
  });
};
