
import { useQuery } from "@tanstack/react-query";
import axiosInstance from "@/lib/axiosInstance";
import { useRoleStore } from "@/app/[locale]/pricing/store/roleStore";
import { useEffect } from "react";
import { toggle } from "@nextui-org/theme";

export interface Role {
  id: string;
  title: string;
  description: string;
  cost: number;
  category: string;
}

interface GetRolesResponse {
  success: boolean;
  data: {
    roles: Role[];
  };
}

const fetchRoles = async (locale: string) => {
  try {
    const { data }: { data: GetRolesResponse } = await axiosInstance.get(
      "/api/roles",
      {
        headers: {
          "Content-Type": "application/json",
          locale: locale,
        },
      }
    );
    return data;

  } catch (error) {
    console.error(error);
  }
};

export const useFetchRoles = (locale: string) => {
  const { setRoles, toggleRole, roles, checkedRoles } = useRoleStore(); // Get the setRoles action from the store

  const { data, error, isLoading } = useQuery({
    queryKey: ["roles", locale],
    queryFn: () => fetchRoles(locale),
    retry: 3,           // number of retry attempts (default is 3)
    retryDelay: attempt => Math.min(1000 * 2 ** attempt, 30000),

  });


  useEffect(() => {
    if (data) {
      setRoles(data?.data?.roles!);
    }
    data?.data, roles?.map(r => {
      if (['management', 'design', 'mobile', 'backend', 'devops', 'qa'].includes(r.category)) {
        toggleRole(r.id, r.title, r.description, r.category, r.cost)
      }

    })
  }, [data, setRoles, roles, isLoading, locale]);


  return {
    data: data?.data.roles!,
    error,
    isLoading,
  };
};