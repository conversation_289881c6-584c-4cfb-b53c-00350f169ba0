import { create } from "zustand";

export interface CheckedRole {
  id: string;
  number: number;
  title: string;
  description: string;
  cost: number;
  category: string;
}

export interface Role {
  id: string;
  title: string;
  category: string;
  cost: number;
  description: string;
}

interface RoleStoreState {
  roles: Role[]; // New state to store the fetched roles
  checkedRoles: CheckedRole[];
  toggleRole: (roleId: string, roleTitle: string, roleDescription: string, category: string, cost: number) => void;
  updateRoleNumber: (roleId: string, number: number) => void;
  setRoles: (roles: Role[]) => void; // New action to set the roles
}

export const useRoleStore = create<RoleStoreState>((set) => ({
  roles: [],
  checkedRoles: [],
  toggleRole: (roleId: string, roleTitle: string, roleDescription: string, category: string, cost: number) => {
    set((state) => {
      const isChecked = state.checkedRoles.some((role) => role.id === roleId);
      return {
        checkedRoles: isChecked
          ? state.checkedRoles.filter((role) => role.id !== roleId)
          : [
            ...state.checkedRoles,
            { id: roleId, title: roleTitle, description: roleDescription, cost: cost, category: category, number: 1 },
          ],
      };
    });
  },
  updateRoleNumber: (roleId, number) => {
    set((state) => ({
      checkedRoles: state.checkedRoles.map((role) =>
        role.id === roleId ? { ...role, number } : role
      ),
    }));
  },
  setRoles: (roles) => set({ roles }),
}));
