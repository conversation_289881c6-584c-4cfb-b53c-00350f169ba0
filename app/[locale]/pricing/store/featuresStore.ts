
import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

export type FeatureType = "Basic" | "Optional" | "Add_On";

export interface CheckedFeature {
  id: string;
  title: string;
  type: FeatureType;
  backend_time: number;
  web_time: number;
  mobile_time: number;
  category: string;
  role_category: string,
  QA_time: number;
  design_time: number;
  base_price: number;
  devops_time: number;
}

interface FeatureStoreState {
  checkedFeatures: CheckedFeature[];
  platform: string[];
  toggleFeature: (feature: CheckedFeature) => void;
  togglePlatform: (platform: string) => void;
  setFeatures: (features: CheckedFeature[]) => void;
}

export const useFeatureStore = create<FeatureStoreState>()(
  persist(
    (set) => ({
      checkedFeatures: [],
      platform: ["mobile"],
      toggleFeature: (feature) => {
        set((state) => {
          const isChecked = state.checkedFeatures.some(
            (f) => f.id === feature.id
          );
          return {
            checkedFeatures: isChecked
              ? state.checkedFeatures.filter((f) => f.id !== feature.id)
              : [...state.checkedFeatures, feature],
          };
        });
      },
      togglePlatform: (platform) => {
        set((state) => ({
          platform: state.platform.includes(platform)
            ? state.platform.filter((p) => p !== platform)
            : [...state.platform, platform],
        }));
      },
      setFeatures: (features) => {
        set({
          checkedFeatures: features
            .filter((feature) => feature.type === "Basic") // Only Basic features
            .map((feature) => ({
              id: feature.id,
              title: feature.title,
              type: feature.type,
              backend_time: feature.backend_time,
              web_time: feature.web_time,
              mobile_time: feature.mobile_time,
              category: feature.category,
              role_category: feature.role_category,
              QA_time: feature.QA_time,
              design_time: feature.design_time,
              base_price: feature.base_price,
              devops_time: feature.devops_time,
            })),
        });
      },
    }),
    {
      name: "feature-storage",
      storage: createJSONStorage(() => sessionStorage),
      // Only persist `checkedFeatures`, exclude `platform`
      partialize: (state) => ({ checkedFeatures: state.checkedFeatures }),
    }
  )
);
