// @/store/useStepStore.ts
import { create } from 'zustand';

type StepState = {
  activeStep: number;
  steps: string[];
  setActiveStep: (step: number) => void;
};

export const useStepStore = create<StepState>((set) => ({
  activeStep: 0,
  steps: [
    "Platform",
    "Basic Features",
    "Advanced Features",
    "Support",
    "Marketing"
  ],
  setActiveStep: (step) => {
    set((state) => {
      if (state.activeStep === step) return state;
      return { activeStep: step };
    });
  },
}));