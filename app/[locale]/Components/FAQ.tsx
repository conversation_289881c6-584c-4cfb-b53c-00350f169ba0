import React from "react";
import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const FAQ = ({ locale, t }: any) => {
  const isArabic = locale === "ar";
  const fontClass = isArabic ? "zain-regular" : "outfit";

  return (
    <section
      className={`${
        isArabic
          ? `${fontClass} text-[2.6rem] md:text-[4.6rem] lg:text-[5.4rem] `
          : `${fontClass} text-[2.6rem] md:text-[4.6rem] lg:text-[5.75rem]`
      } bg-none`}
    >
      <div className="w-[90%]  m-auto pt-[10%] px-4">
        {[1, 2, 3, 4, 5, 6].map((index) => {
          return (
            <div key={index} className="w-[20] ">
              <h2 className={`text-[32px] w-fit font-semibold ${isArabic? 'text-right' :'text-left'}`}>
                {t(`faq.${index}.title`)}
              </h2>
              <div className="py-4 px-4">
                  <Accordion type="single" collapsible  className="flex flex-col md:flex-row gap-3 flex-wrap">
                {[1, 2, 3, 4, 5].map((sub,inde) => (
                  t(`faq.${index}.${sub}.q`).length>0 &&  
                  
                  <AccordionItem value={`${inde+sub}`}  key={`${inde+sub}`} className="my-1 w-[45%]">
                  <div className="bg-slate-200 rounded-lg shadow-md ">

                      <AccordionTrigger  className={`text-xl md:text-[22px]  p-5   flex ${isArabic? 'flex-row-reverse  text-right':'flex-row'}`}>
                        {t(`faq.${index}.${sub}.q`)}
                      </AccordionTrigger>
                      <AccordionContent >
                        <p className={`text-[22px] leading-8  p-7 ${isArabic? 'text-right':'text-left'}`}>

                      {t(`faq.${index}.${sub}.a`)}
                        </p>
                      </AccordionContent>
                  </div>
                    </AccordionItem>
                ))}
                </Accordion>
              </div>
            </div>
          );
        })}
      </div>
    </section>
  );
};

export default FAQ;
