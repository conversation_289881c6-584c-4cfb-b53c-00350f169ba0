import React from "react";
import ProgressBar from "./ProgressBar";
import StepOne from "../../../../public/icons/ProcessStepOne.svg";
import StepTwo from "../../../../public/icons/ProcessStepTwo.svg";
import StepThree from "../../../../public/icons/ProcessStepThree.svg";
import StepFour from "../../../../public/icons/ProcessStepFour.svg";

interface WorksSectionProps {
  t: (key: string) => string;
  locale: string;
}

const steps = [
  {
    titleKey: "worksSection.steps.0.title",
    descriptionKey: "worksSection.steps.0.description",
    Img: StepOne,
  },
  {
    titleKey: "worksSection.steps.1.title",
    descriptionKey: "worksSection.steps.1.description",
    Img: StepTwo,
  },
  {
    titleKey: "worksSection.steps.2.title",
    descriptionKey: "worksSection.steps.2.description",
    Img: StepThree,
  },
  {
    titleKey: "worksSection.steps.3.title",
    descriptionKey: "worksSection.steps.3.description",
    Img: StepFour,
  },
];

const WorksSection: React.FC<WorksSectionProps> = ({ t, locale }) => {
  return (
    <main
      className={`w-full relative h-full  flex xl:block flex-col items-center my-[4rem]`}
      dir={locale === "ar" ? "rtl" : ""}
    >
      {/* <Image
        src={worksBg}
        alt="Hero Section Background"
        className="absolute w-full h-full z-[-10] object-cover object-center  mask-fade-up"
      ></Image> */}
      
      <section aria-labelledby="works-section-heading" className="p-4">
        {/* Main Heading */}
        <h2
          className={`flex justify-center text-[2rem] xl:text-[4rem] text-center  text-[#024e6e] font-bold ${
            locale === "ar" ? "zain-regular" : "outfit "
          }`}
        >
          {t("worksSection.Header")}
        </h2>
        <h2
          id="works-section-heading"
          className={`flex justify-center text-[2rem] xl:text-[3rem] font-normal text-center  text-[#516065] ${
            locale === "ar" ? "zain-regular" : "outfit "
          }`}
        >
          {t("worksSection.title")} {/* Translated */}
        </h2>

        {/* Subheading and Button */}
        <div
          className={`flex flex-col xl:flex-row items-center justify-center px-[3rem] xl:px-[12rem] `}
        >
          <h3
            className={`max-w-[50rem] font-normal text-[1.4rem] opacity-60 text-[#003449] text-center my-4 ${
              locale === "ar"
                ? "zain-regular"
                : "outfit "
            }`}
          >
            {t("worksSection.subheading")} {/* Translated */}
          </h3>

          {/* Button to 'Our Works' */}
          {/* <a href="/works" aria-label={t("worksSection.buttonLabel")}>
            <Image
              src={worksbtn}
              alt={t("worksSection.buttonLabel")}
              className={`w-[7.5rem] h-[7.5rem] hidden`}
            />
          </a> */}
        </div>
      </section>
      <section dir="ltr">
        {/* Progress Bar */}
        <ProgressBar
          steps={steps.map((step) => ({
            ...step,
            title: t(step.titleKey),
            description: t(step.descriptionKey),
          }))}
          locale={locale}
        />
      </section>
    </main>
  );
};

export default WorksSection;
