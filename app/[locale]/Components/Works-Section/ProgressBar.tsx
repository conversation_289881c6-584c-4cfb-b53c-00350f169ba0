"use client";
import Image from "next/image";
import React, { useState } from "react";

interface Step {
  title: string;
  description: string;
  Img: any;
}

interface ProgressBarProps {
  steps: Step[];
  locale: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ steps, locale }) => {
  const [currentStep, setCurrentStep] = useState(0);

  const handleStepClick = (index: number) => {
    setCurrentStep(index);
  };

  const renderStep = (step: Step, index: number) => (
    <div
      key={index}
      onClick={() => handleStepClick(index)}
      className={`flex flex-col items-center w-[20rem] min-h-48 mx-8  mb-24 mt-8 p-[0.5rem] rounded-xl shadow-xl text-center transition-all duration-500 cursor-pointer select-none ${
        currentStep === index
          ? "transform scale-105 bg-[#003449] text-white"
          : "bg-[#ffffff] text-[#003449]"
      }`}
    >
      <Image
        src={step.Img}
        alt={step.title}
        className={`w-16 ${currentStep === index ? "invert" : ""}`}
      />
      <div>
        <h3
          className={`font-bold pt-4 ${
            locale === "ar" ? "zain-regular text-lg" : "montserrat text-lg"
          }`}
        >
          {step.title}
        </h3>
        <p
          className={`${
            locale === "ar" ? "zain-regular text-sm" : "poppins-regular text-xs"
          }`}
        >
          {step.description}
        </p>
      </div>
    </div>
  );

  return (
    <div className="flex flex-col items-center w-full h-full p-4 mx-auto mt-16">
      {/* Horizontal ProgressBar for large screens */}
      <div className="relative hidden lg:flex justify-between items-center w-[60%] mb-8">
        <div className="absolute top-1/2 w-[98%] h-1 bg-[#98bfcb] transform -translate-y-1/2"></div>
        <div
          className="absolute top-1/2 left-2 h-1 bg-[#003449] transform -translate-y-1/2 transition-all duration-300"
          style={{ width: `${(currentStep / (steps.length - 1)) * 98}%` }}
        ></div>
        {steps.map((_, index) => (
          <div
            key={index}
            className="relative z-10 flex flex-col items-center cursor-pointer"
            onClick={() => handleStepClick(index)}
          >
            <div
              className={`w-8 h-8 flex items-center justify-center rounded-full border-2  ${
                currentStep >= index
                  ? "bg-[#71B331] border-[#71B331]"
                  : "bg-[#98bfcb] border-[#98bfcb]"
              }`}
            >
              <span className="text-white">{index + 1}</span>
            </div>
          </div>
        ))}
      </div>

      {/* Vertical ProgressBar for small screens */}
      <div className="lg:hidden w-full flex flex-col items-center mb-8">
        {steps.map((step, index) => (
          <div
            key={index}
            onClick={() => handleStepClick(index)}
            className={`relative flex items-center w-full max-w-md mb-8 cursor-pointer p-4 rounded-xl shadow transition-all duration-300 ${
              currentStep === index ? "bg-[#003449] text-white" : "bg-[#DFDFDF]"
            }`}
          >
            <div
              className={`absolute w-8 h-8 flex items-center justify-center rounded-full border-2 left-4 transition-colors duration-300 ${
                currentStep >= index
                  ? "bg-[#71B331] border-[#71B331]"
                  : "bg-gray-300 border-gray-300"
              }`}
            >
              <span className="text-white">{index + 1}</span>
            </div>

            {index < steps.length - 1 && (
              <div
                className={`absolute left-7 top-20 h-full w-0.5 z-[-1] transition-all duration-400  ${
                  currentStep > index ? "bg-[#003449]" : "bg-[#DFDFDF]"
                }`}
              ></div>
            )}
            <div className="flex items-center ml-16">
              <Image
                src={step.Img}
                alt={step.title}
                className={`w-16 mr-4 transition-transform duration-400  ${
                  currentStep === index ? "invert" : ""
                }`}
              />
              <div>
                <h3
                  className={`${
                    locale === "ar"
                      ? "zain-regular text-lg font-bold"
                      : "text-lg font-bold"
                  }`}
                >
                  {step.title}
                </h3>
                <p
                  className={`transition-all text-lg duration-400 ${
                    currentStep === index
                      ? "opacity-100 text-sm"
                      : "opacity-0 text-xs"
                  } ${locale === "ar" ? "zain-regular" : ""}`}
                >
                  {step.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Text boxes for large screens */}
      <div className="hidden lg:flex justify-around w-[80%] flex-nowrap ">
        {steps.map(renderStep)}
      </div>
    </div>
  );
};

export default ProgressBar;
