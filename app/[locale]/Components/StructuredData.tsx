import React from 'react';

interface StructuredDataProps {
  type: 'organization' | 'service' | 'pricing' | 'faq';
  data?: any;
  locale?: string;
}

const StructuredData: React.FC<StructuredDataProps> = ({ type, data, locale = 'en' }) => {
  const getStructuredData = () => {
    const baseUrl = 'https://www.go-tru.com';
    
    switch (type) {
      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "GoTru",
          "alternateName": "Go-Tru",
          "url": baseUrl,
          "logo": `${baseUrl}/icon.webp`,
          "description": locale === 'ar' 
            ? "شركة رائدة في تطوير البرمجيات والاستشارات التقنية متخصصة في تطوير الويب والتطبيقات والخدمات السحابية"
            : "Leading software development and tech consulting agency specializing in web development, mobile apps, and cloud services",
          "address": {
            "@type": "PostalAddress",
            "addressCountry": "AE",
            "addressRegion": "Dubai"
          },
          "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["English", "Arabic"]
          },
          "sameAs": [
            "https://www.linkedin.com/company/gotru",
            "https://twitter.com/gotru"
          ],
          "offers": {
            "@type": "AggregateOffer",
            "priceCurrency": "AED",
            "lowPrice": "5000",
            "highPrice": "30000",
            "description": locale === 'ar'
              ? "خدمات تطوير البرمجيات والمواقع والتطبيقات"
              : "Software development, website and mobile app services"
          }
        };

      case 'service':
        return {
          "@context": "https://schema.org",
          "@type": "Service",
          "name": data?.title || "Software Development Services",
          "description": data?.description || "Professional software development services",
          "provider": {
            "@type": "Organization",
            "name": "GoTru",
            "url": baseUrl
          },
          "areaServed": {
            "@type": "Country",
            "name": "United Arab Emirates"
          },
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Software Development Services",
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "E-commerce Development",
                  "description": "Professional e-commerce website development"
                },
                "priceCurrency": "AED",
                "price": "5000"
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Website Development",
                  "description": "Custom website development services"
                },
                "priceCurrency": "AED",
                "price": "3000"
              }
            ]
          }
        };

      case 'pricing':
        return {
          "@context": "https://schema.org",
          "@type": "PriceSpecification",
          "name": data?.title || "Software Development Pricing",
          "description": data?.description || "Transparent software development pricing",
          "priceCurrency": "AED",
          "price": data?.price || "5000",
          "priceType": "https://schema.org/SRP",
          "valueAddedTaxIncluded": false,
          "validFrom": new Date().toISOString(),
          "validThrough": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
        };

      case 'faq':
        return {
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": [
            {
              "@type": "Question",
              "name": locale === 'ar' 
                ? "كم تكلفة تطوير موقع إلكتروني؟"
                : "How much does website development cost?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": locale === 'ar'
                  ? "تتراوح تكلفة تطوير المواقع من 3000 إلى 15000 درهم حسب التعقيد والميزات المطلوبة."
                  : "Website development costs range from 3,000 to 15,000 AED depending on complexity and required features."
              }
            },
            {
              "@type": "Question",
              "name": locale === 'ar'
                ? "كم تكلفة تطوير متجر إلكتروني؟"
                : "How much does e-commerce development cost?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": locale === 'ar'
                  ? "تتراوح تكلفة تطوير المتاجر الإلكترونية من 5000 إلى 30000 درهم حسب عدد المنتجات والميزات."
                  : "E-commerce development costs range from 5,000 to 30,000 AED depending on product count and features."
              }
            },
            {
              "@type": "Question",
              "name": locale === 'ar'
                ? "ما هي العوامل المؤثرة على أسعار تطوير البرمجيات؟"
                : "What factors affect software development pricing?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": locale === 'ar'
                  ? "تشمل العوامل: تعقيد المشروع، التقنيات المستخدمة، التصميم المخصص، التكامل مع أنظمة خارجية، والجدول الزمني."
                  : "Factors include: project complexity, technologies used, custom design, third-party integrations, and timeline."
              }
            }
          ]
        };

      default:
        return null;
    }
  };

  const structuredData = getStructuredData();

  if (!structuredData) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  );
};

export default StructuredData;
