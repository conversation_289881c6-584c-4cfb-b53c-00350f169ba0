import React from "react";
import WorksSection from "./Works-Section/WorksSection";
import ContactUs from "./ContactUs";
import Footer from "./Footer";
import Image from "next/image";
import HeroSection from "./HeroSection";
import AboutUsSection from "./AboutUsSection";
import ServicesSection from "./ServicesSection";
import TechnologiesSection from "./Tech-Section/TechnologiesSection";
import PortfolioSection from "./Portfolio-Section/PortfolioSection";
import NavBar from "./NavBar";
import arabic_letter from "../../../public/bgs/LargeBG.svg";
import PricingSection from "../pricing/components/PricingSection";

export default async function Sections({
  t,
  locale,
}: {
  t: any;
  locale: string;
}) {

  return (
    <main className="w-full h-full">
      <div className="relative overflow-hidden">
        <Image
          src={arabic_letter}
          alt="Arabic Calligraphy Background"
          className="absolute z-[-10] w-full h-full object-cover object-center opacity-60 "
          priority
        />
        <header className="flex justify-center items-center">
          <NavBar locale={locale} />
        </header>

        {/* Pass translations to the HeroSection */}
        <section id="HeroSection">
          <HeroSection t={t} locale={locale} />
        </section>

        <section id="About" aria-labelledby="about-heading">
          <h2 id="about-heading" className="sr-only">
            About Us
          </h2>
          <AboutUsSection t={t} locale={locale} />
        </section>

        <section id="services" aria-labelledby="services-heading">
          <h2 id="services-heading" className="sr-only">
            Our Services
          </h2>
          <ServicesSection t={t} locale={locale} />
        </section>

        <section id="Technologies" aria-labelledby="technologies-heading">
          <h2 id="technologies-heading" className="sr-only">
            Technologies We Use
          </h2>
          <TechnologiesSection t={t} locale={locale} />
        </section>

        <section id="Portfolio" aria-labelledby="portfolio-heading">
          <h2 id="portfolio-heading" className="sr-only">
            Our Portfolio
          </h2>
          <PortfolioSection t={t} locale={locale} />
        </section>

        <section id="Pricing" aria-labelledby="pricing-heading">
          <h2 id="pricing-heading" className="sr-only">
            Pricing
          </h2>
          <PricingSection t={t} locale={locale} />
        </section>

        <section id="work" aria-labelledby="work-heading">
          <h2 id="work-heading" className="sr-only">
            Our Work
          </h2>
          <WorksSection t={t} locale={locale} />
          <footer id="Contact" aria-labelledby="contact-heading">
            <ContactUs t={t} locale={locale} />
            <Footer t={t} locale={locale} />
          </footer>
        </section>
      </div>
    </main>
  );
}
