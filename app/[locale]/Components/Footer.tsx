import React from "react";
import Image from "next/image";
import FooterLogo from "../../../public/icons/emptyLogo.svg";
import mail from "../../../public/icons/mail.svg";
import Phone from "../../../public/icons/Phone.svg";
import location from "../../../public/icons/location.svg";

interface FooterProps {
  t: (key: string) => string;
  locale: string;
}

const Footer: React.FC<FooterProps> = ({ t, locale }) => {
  return (
    <footer className="w-full h-full bg-[#e2e2e25e] z-10">
      <div className="pb-[3rem] border-b-2 border-b-[#71B331] mx-[2rem] xl:mx-[9rem]">
        <section className="flex flex-col xl:flex-row justify-between items-end h-auto xl:h-[12rem] px-4 space-y-8 xl:space-y-0">
          {/* Logo Section */}
          <div className="w-full xl:w-[13%] space-y-6 items-center xl:items-start hidden xl:flex flex-col">
            <div className="flex items-center w-full justify-between">
              <Image
                src={FooterLogo}
                alt="GoTru Logo"
                className="object-contain w-[5.5rem]"
                loading="lazy"
              />
              <h2 className="text-[#005171] text-[2rem] font-medium montserrat pt-1">
                GoTru
              </h2>
            </div>
            <p
              className={` text-center xl:text-left ${
                locale === "ar"
                  ? "zain-regular text-justify text-nowrap"
                  : "  text-justify text-nowrap"
              }`}
            >
              {t("footer.tagline")}
            </p>
          </div>

          {/* Contact Section */}
          <div
            className="text-[#003449] poppins-regular flex flex-col w-full xl:w-auto space-y-4  items-start justify-start  "
            dir={locale === "ar" ? "rtl" : ""}
          >
            <h2
              className={`font-bold  leading-7 ${
                locale === "ar" ? "zain-regular text-[1.3rem]" : "text-[1.1rem]"
              }`}
            >
              {t("footer.contactTitle")}
            </h2>
            <address className="text-[1rem] xl:text-[0.9rem] leading-8 space-y-2 not-italic">
              <p className="flex items-center">
                <Image
                  src={Phone}
                  alt="Phone Icon"
                  className={`w-[1.5rem] h-[1.5rem]  ${
                    locale === "ar" ? "ml-2" : "mr-2"
                  }`}
                />
                <a
                  href="tel:+971528281927"
                  aria-label={`Call us at +971 52 828 1927`}
                  dir="ltr"
                >
                  +971 52 828 1927
                </a>
              </p>
              <p className="flex items-center">
                <Image
                  src={location}
                  alt="Location Icon"
                  className={`w-[1.5rem] h-[1.5rem]  ${
                    locale === "ar" ? "ml-2" : "mr-2"
                  }`}
                />
                Ires Bay building, Business Bay, Dubai
              </p>
              <p className="flex items-center">
                <Image
                  src={mail}
                  alt="Mail Icon"
                  className={`w-[1.5rem] h-[1.5rem]  ${
                    locale === "ar" ? "ml-2" : "mr-2"
                  }`}
                />
                <a
                  href="mailto:<EMAIL>"
                  aria-label={`Email <NAME_EMAIL>`}
                >
                  <EMAIL>
                </a>
              </p>
            </address>
          </div>
        </section>
      </div>
      <div className="flex justify-center py-[2rem]">
            <h4 className="text-[1rem] font-medium poppins-regular text-[#003449]">
              © 2024 Go Tru. All Rights Reserved.
            </h4>
          </div>
    </footer>
  );
};

export default Footer;
