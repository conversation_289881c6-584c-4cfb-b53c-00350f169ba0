"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import gotruLogo from "../../../public/images/Icon.webp";
import MenuIcon from "../../../public/icons/MenuIcon.svg";
import {
  motion,
  AnimatePresence,
  useScroll,
  useMotionValueEvent,
} from "framer-motion";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import LanguageChanger from "./LanguageChanger";
import Link from "next/link";

interface NavItem {
  name: string;
  link: string;
  path: string;
  icon?: JSX.Element;
}

const sections = [
  { name: "about", id: "About", path: "About" },
  { name: "services", id: "services", path: "services" },
  { name: "technologies", id: "Technologies", path: "technologies" },
  { name: "portfolio", id: "Portfolio", path: "portfolio" },
  { name: "work", id: "work", path: "work" },
  { name: "contact", id: "contactus", path: "contactus" },
];

export const FloatingNav = ({
  navItems,
  className,
  locale,
}: {
  navItems: NavItem[];
  className?: string;
  locale: string;
}) => {
  const { scrollYProgress } = useScroll();
  const [visible, setVisible] = useState(false);

  useMotionValueEvent(scrollYProgress, "change", (current) => {
    if (typeof current === "number") {
      let direction = current! - scrollYProgress.getPrevious()!;
      if (scrollYProgress.get() < 0.05) {
        setVisible(false);
      } else {
        setVisible(direction < 0);
      }
    }
  });

  return (
    <AnimatePresence mode="wait">
      <motion.nav
        initial={{
          opacity: 1,
          y: -100,
        }}
        animate={{
          y: visible ? 0 : -100,
          opacity: visible ? 1 : 0,
        }}
        transition={{
          duration: 0.2,
        }}
        className={cn(
          "flex w-[90%] sm:max-w-fit fixed top-10 inset-x-0 mx-auto border-[0.05rem] border-[#65a839] rounded-full bg-[#003449] shadow-[0px_2px_3px_-1px_rgba(0,0,0,0.1),0px_1px_0px_0px_rgba(25,28,33,0.02),0px_0px_0px_1px_rgba(25,28,33,0.08)] z-[60] items-center justify-center space-x-[1.3rem] px-[2rem] py-[1rem]",
          className
        )}
        aria-label="Floating navigation"
      >
        {navItems.map((navItem, idx) => (
          <Link
            key={`link=${idx}`}
            href={`/#${navItem.path}`}
            className={cn(
              "relative items-center flex justify-evenly text-white hover:text-[#3c758b] font-medium duration-300 cursor-pointer"
            )}
            // scroll={false}
            // onClick={(e) => {
            //   e.preventDefault();
            //   document.getElementById(navItem.link)?.scrollIntoView({
            //     behavior: "smooth",
            //   });
            // }}
          >
            <span
              className={`text-[0.7rem] sm:text-[0.9rem] md:text-[1.2rem] zain-regular`}
            >
              {navItem.name}
            </span>
          </Link>
        ))}
        <Link
          className={cn(
            "relative items-center flex justify-evenly text-white hover:text-[#3c758b] font-medium duration-300 cursor-pointer"
          )}
          href={`/${locale}/faq`}
        >
          <span className="text-[0.7rem] sm:text-[0.9rem] md:text-[1.2rem] zain-regular">

          FAQ
          </span>
        </Link>
      </motion.nav>
    </AnimatePresence>
  );
};

const NavBar: React.FC<{ locale: string }> = ({ locale }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  useEffect(() => {
    document.body.style.overflow = isMenuOpen ? "hidden" : "unset";
  }, [isMenuOpen]);

  const { t, i18n } = useTranslation();

  useEffect(() => {
    i18n.changeLanguage(locale);
  }, [locale, i18n]);

  const translatedSections: NavItem[] = sections.map((section) => ({
    name: t(section.name),
    link: section.id,
    path: section.path,
  }));

  return (
    <header className="w-full ">
      <FloatingNav navItems={translatedSections} locale={locale} />

      <nav
        className="flex justify-center absolute w-full  z-[50] bg-white"
        aria-label="Main navigation"
      >
        <div className="flex justify-between items-center w-[100%] max-w-[1700px] h-[4rem] md:h-[5rem] mt-[1rem] p-[1.5rem]">
          <Link href="/" className="flex items-center space-x-2 cursor-pointer">
            <Image
              src={gotruLogo}
              className="w-[3.5rem] object-contain"
              alt="Go-Tru Logo"
              width={56}
              height={56}
              priority
              loading="eager"
            />
            <h1 className="text-[#005171] text-[2rem] font-medium montserrat pt-1">
              {t("go_tru")}
            </h1>
          </Link>

          <div className="hidden lg:flex space-x-[3rem] w-full items-center">
            <ul
              className={`hidden lg:flex space-x-[3rem] justify-end w-full text-[#003449] ${
                locale === "ar"
                  ? "zain-regular text-[1.2rem] font-extrabold"
                  : "montserrat text-[1rem] font-bold"
              }`}
            >
              {translatedSections.map((section) => (
                <li key={section.link} className="cursor-pointer">
                  {section.path === "portfolio" ? (
                    <Link href={`/${locale}/portfolio`}>
                      {section.name}
                    </Link>
                  ) : (
                    <Link
                      href={`/#${section.path}`}
                      // scroll={false}
                      // onClick={(e) => {
                      //   e.preventDefault();
                      //   document.getElementById(section.link)?.scrollIntoView({
                      //     behavior: "smooth",
                      //   });
                      // }}
                    >
                      {section.name}
                    </Link>
                  )}
                </li>
              ))}
              <li>
                <Link href={`/${locale}/pricing`}>{t("pricing")}</Link>
              </li>
              <li>
                <Link href={`/${locale}/faq`}>FAQ</Link>
              </li>
            </ul>

            <LanguageChanger />
          </div>

          <button
            className="flex lg:hidden"
            onClick={toggleMenu}
            aria-expanded={isMenuOpen}
            aria-label="Toggle menu"
          >
            <div className="w-8 h-8 rounded-lg flex items-center justify-center">
              <Image
                src={MenuIcon}
                alt="Menu Icon"
                width={32}
                height={32}
                className="w-8 h-8"
                loading="eager"
              />
            </div>
          </button>
        </div>

        <div
          className={`fixed top-0 right-0 h-screen w-full bg-white bg-opacity-50 backdrop-blur-md shadow-lg transform transition-transform duration-300 ease-in-out ${
            isMenuOpen ? "translate-x-0" : "translate-x-full"
          }`}
          role="dialog"
          aria-modal="true"
          aria-label="Mobile navigation menu"
          aria-hidden={!isMenuOpen}
        >
          <div className="flex justify-end p-4">
            <button
              onClick={toggleMenu}
              className="text-[2rem]"
              aria-label="Close menu"
            >
              ×
            </button>
          </div>
          <nav aria-label="Mobile navigation">
            <ul className="flex flex-col items-center space-y-[2rem] p-4 font-bold text-[#003449] text-[1.5rem] montserrat">
              {translatedSections.map((section) => (
                <li
                  key={section.link}
                  className={`cursor-pointer border-b-2 border-[#71b331] w-[11rem] text-center ${
                    locale === "ar" ? "zain-regular" : ""
                  }`}
                >
                  {section.path === "portfolio" ? (
                    <Link
                      href={`/${locale}/portfolio`}
                      onClick={toggleMenu}
                    >
                      {section.name}
                    </Link>
                  ) : (
                    <Link
                      href={`/${locale}#${section.path}`}
                      scroll={false}
                      onClick={(e) => {
                        e.preventDefault();
                        document.getElementById(section.link)?.scrollIntoView({
                          behavior: "smooth",
                        });
                        toggleMenu();
                      }}
                    >
                      {section.name}
                    </Link>
                  )}
                </li>
              ))}
              <li
                className={`cursor-pointer border-b-2 border-[#71b331] w-[11rem] text-center ${
                  locale === "ar" ? "zain-regular" : " outfit"
                }`}
              >
                <Link href={`/${locale}/pricing`} onClick={toggleMenu}>{t("pricing")}</Link>
              </li>
              <li
                className={`cursor-pointer border-b-2 border-[#71b331] w-[11rem] text-center ${
                  locale === "ar" ? "zain-regular" : " outfit"
                }`}
              >
                <Link href={`/${locale}/faq`} onClick={toggleMenu}>FAQ</Link>
              </li>
            </ul>
          </nav>

          <div className="flex justify-center mt-8">
            <LanguageChanger />
          </div>
        </div>
      </nav>
    </header>
  );
};

export default NavBar;
