import Image from "next/image";
import Link from "next/link";
import gotru<PERSON>ogo from "../../../public/icons/TransperantGotruLogo.svg";

interface HeroSectionProps {
  t: (key: string) => string;
  locale: string;
}

const HeroSection: React.FC<HeroSectionProps> = ({ t, locale }) => {
  const isArabic = locale === "ar";
  const fontClass = isArabic ? "zain-regular" : "outfit";

  return (
    <main className="w-full relative flex flex-col items-center justify-center space-y-[4rem] pt-[8rem] md:pt-[11rem]">
      {/* <Image
        src={Herobg}
        alt="Hero Section Background"
        className="absolute w-full h-full z-[-10] object-cover object-center  mask-fade-up"
      ></Image> */}
      <section
        className={`text-[#005171] text-center flex flex-col items-center font-normal tracking-tighter
          ${
            isArabic
              ? `${fontClass} text-[2.6rem] md:text-[4.6rem] lg:text-[5.4rem] leading-[3.2rem] md:leading-[5.2rem] lg:leading-[6rem]`
              : `${fontClass} text-[2.6rem] md:text-[4.6rem] lg:text-[5.75rem] leading-[3.2rem] md:leading-[5.2rem] lg:leading-[6rem]`
          }`}
      >
        <h1 className="">
          {t("heroHeadline")}
        </h1>
        <div
          className={`flex items-center justify-center gap-4 md:gap-2 ${
            isArabic ? "flex-col-reverse md:flex-row" : "flex-col md:flex-row"
          }`}
        >
          <p className="">
            {t("outcome")}
          </p>
          <div className="bg-[#F1FFE9] rounded-full w-[8.5rem] md:w-[12.75rem] h-[8.5rem] md:h-[12.75rem] flex items-center justify-center flex-shrink-0">
            <Image
              src={gotruLogo}
              alt="GoTru Logo"
              className="w-[6.78rem] md:w-[8.78rem] object-contain"
              width={140}
              height={140}
              priority
              loading="eager"
              sizes="(max-width: 768px) 6.78rem, 8.78rem"
            />
          </div>
          <p className="">
            {t("delivered")}
          </p>
        </div>
      </section>

      <p
        className={`max-w-[23rem] lg:max-w-[35rem] text-center font-medium opacity-80 text-[#2C2C2C] min-h-[8rem] 
          ${
            isArabic
              ? `${fontClass} text-[1.2rem] md:text-[1.4rem] leading-[1.8rem] md:leading-[2.1rem]`
              : `montserrat text-[1rem] md:text-[1.2rem] leading-[1.5rem] md:leading-[1.8rem]`
          }`}
        dir={isArabic ? "rtl" : "ltr"}
      >
        {t("heroDescription.textBeforeBold1")}{" "}
        <strong className="text-[#0B81B0] inline-block">
          {t("heroDescription.bold1")}
        </strong>{" "}
        {t("heroDescription.textBetweenBold1Bold2")}{" "}
        <strong className="text-[#0B81B0] inline-block">
          {t("heroDescription.bold2")}
        </strong>{" "}
        {t("heroDescription.textBetweenBold2Bold3")}{" "}
        <strong className="text-[#0B81B0] inline-block">
          {t("heroDescription.bold3")}
        </strong>{" "}
        {t("heroDescription.textAfterBold3")}
      </p>

      <Link
        href="https://wa.me/+971528281927"
        target="_blank"
        rel="noopener"
        className="group px-[1.7rem] lg:px-[2.5rem] py-[1rem] lg:py-[1.2rem] rounded-full bg-[#005274] flex items-center space-x-[2rem] lg:space-x-[4rem]"
      >
        <span
          className={`text-white font-extrabold flex items-center whitespace-nowrap
            ${
              isArabic
                ? `${fontClass} tracking-[0.04rem] text-[1.2rem] lg:text-[1.6rem] leading-[1.8rem] lg:leading-[2.4rem]`
                : `tracking-[0.15rem] text-[0.9rem] lg:text-[1.3rem] leading-[1.35rem] lg:leading-[1.95rem]`
            }`}
        >
          {t("contactUs")}
        </span>
      </Link>
    </main>
  );
};

export default HeroSection;
