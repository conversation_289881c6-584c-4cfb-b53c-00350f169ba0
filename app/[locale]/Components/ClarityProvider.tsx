'use client';

import { ReactNode, useEffect } from 'react';
import Clarity from '@microsoft/clarity';

export default function ClarityProvider({ children }: { children: ReactNode }) {
    useEffect(() => {
        if (typeof window !== 'undefined' && "ron1xy14ae") {
            Clarity.init("ron1xy14ae");

            // Optional: Upgrade sessions that visit your new feature
            Clarity.upgrade('new-feature-tracking');
        }
    }, []);

    return children;
}