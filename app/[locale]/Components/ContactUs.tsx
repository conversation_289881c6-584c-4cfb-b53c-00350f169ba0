import React from "react";
import ContactForm from "./ContactForm";
interface ContactUsProps {
  t: (key: string) => string;
  locale: string;
}

const ContactUs: React.FC<ContactUsProps> = ({ t, locale }) => {
  return (
    <main
      className={`w-full h-full pt-[4rem] pb-[6rem] relative`}
      dir={locale === "ar" ? "rtl" : ""}
      id="contactus"
      
    >
      <div
        className={`flex flex-col justify-center items-center w-full h-full space-y-[4rem] md:space-y-[4rem]`}
      >
        {/* Heading Section */}
        <div
          className={`flex flex-col justify-center items-center text-center  ${
            locale === "ar"
              ? "zain-regular  text-[3.2rem] md:text-[6.3rem] leading-[4rem] md:leading-[6rem]"
              : "outfit  text-[3.2rem] md:text-[6.1rem] leading-[4rem] md:leading-[7.3rem]"
          }`}
        >
          <h2 className={`text-[#01536b] max-w-[100%]`}>
            {t("contactUsText.title")}
          </h2>{" "}
          {/* Translated */}
          <h3 className={`text-[#01536b]`}>
            {t("contactUsText.subtitle")}{" "}
            <span className={`CreativeGradiant`}>
              {t("contactUsText.brand")}
            </span>
          </h3>
        </div>
        {/* Call to Action Button */}
        {/* <div
          className={`rounded-full bg-[#45D66E] hover:bg-[#025370] duration-500 cursor-pointer text-white   px-[2rem] md:px-[3rem] py-[0.75rem] md:py-[1rem] ${
            locale === "ar" ? "zain-regular text-[1.2rem] font-bold tracking-[0.09rem]" : "montserrat text-[1rem] font-bold"
          }`}
        >
          <Link href={"https://wa.me/+971528281927"}>
            {t("contactUsText.button")}
          </Link>{" "}
          {/* Translated */}
        {/* </div> */}
        <ContactForm />
      </div>
    </main>
  );
};

export default ContactUs;
