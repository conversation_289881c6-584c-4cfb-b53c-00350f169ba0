import React from "react";
import Image from "next/image";
import AppDev from "../../../public/icons/MobileDev.svg";
import WebDev from "../../../public/icons/WebDev.svg";
import SysDev from "../../../public/icons/SysDev.svg";
import TechConsultant from "../../../public/icons/TechConsolt.svg";
import CloudServices from "../../../public/icons/CloudServices.svg";
import DataBaseServices from "../../../public/icons/DataBaseServices.svg";

interface ServicesSectionProps {
  t: (key: string) => string;
  locale: string;
}

const ServicesSection: React.FC<ServicesSectionProps> = ({ t, locale }) => {
  const services = [
    {
      titleKey: "servicesText.appDevelopmentTitle",
      descriptionKey: "servicesText.appDevelopmentDescription",
      image: AppDev,
      bgClass: "app-dev-rounded",
    },
    {
      titleKey: "servicesText.webDevelopmentTitle",
      descriptionKey: "servicesText.webDevelopmentDescription",
      image: WebDev,
      bgClass: "web-dev-rounded",
    },
    {
      titleKey: "servicesText.systemDevelopmentTitle",
      descriptionKey: "servicesText.systemDevelopmentDescription",
      image: SysDev,
      bgClass: "sys-dev-rounded",
    },
    {
      titleKey: "servicesText.techConsultingTitle",
      descriptionKey: "servicesText.techConsultingDescription",
      image: TechConsultant,
      bgClass: "tech-consulting-rounded",
    },
    {
      titleKey: "servicesText.cloudServicesTitle",
      descriptionKey: "servicesText.cloudServicesDescription",
      image: CloudServices,
      bgClass: "cloud-services-rounded",
    },
    {
      titleKey: "servicesText.databaseManagementTitle",
      descriptionKey: "servicesText.databaseManagementDescription",
      image: DataBaseServices,
      bgClass: "database-management-rounded",
    },
  ];

  return (
    <section
      className="w-full relative flex items-center justify-center flex-col mt-8 md:mt-12 xl:mt-16"
      aria-labelledby="services-title"
      dir={locale === "ar" ? "rtl" : ""}
    >
      {/* <Image
        src={Servicesbg}
        alt="Hero Section Background"
        className="absolute w-full h-full z-[-10] object-cover object-center opacity-70  mask-fade-up"
      ></Image> */}
      <div className="w-full px-4 md:px-8 xl:w-4/5 flex flex-col items-center justify-center">
        <h2
          id="services-title"
          className={`uppercase text-[#005171] pb-4 ${
            locale === "ar"
              ? "zain-regular font-semibold text-[2.8rem] xl:text-[4.2rem]"
              : "outfit font-medium text-[2.8rem]  xl:text-[4.2rem]"
          }`}
        >
          {t("servicesText.services")}
        </h2>
        <p
          className={`text-[#58626c] font-normal w-full md:w-4/5 xl:w-2/3 leading-relaxed md:leading-loose xl:leading-[4rem] ${
            locale === "ar"
              ? "zain-regular text-[1.8rem]  xl:text-[2.8em]"
              : "outfit text-[1.8rem]  xl:text-[2.8rem]"
          } text-center`}
        >
          {t("servicesText.servicesHeadline")}
        </p>

        <div
          className="w-full grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 mt-8 md:mt-8  gap-0.5 bg-[#e7e7e7] rounded-xl md:rounded-2xl overflow-hidden shadow-lg md:shadow-xl xl:shadow-2xl"
          role="list"
          aria-label="Our services"
        >
          {services.map((service, index) => (
            <article
              key={index}
              className={`flex flex-row md:flex-col bg-[#fafeff] items-center md:items-start p-4 md:p-6 xl:p-8 ${service.bgClass} 
                hover:bg-[#003449] hover:text-white duration-500 group h-[8rem] md:h-[18rem]`}
              role="listitem"
              aria-labelledby={`service-title-${index}`}
            >
              <Image
                src={service.image}
                alt={`${t(service.titleKey)} Icon`}
                className="w-12 h-12 md:w-16 md:h-16 mb-0 md:mb-6 xl:mb-8 bg-[#3ba2cd] p-2 rounded-xl md:rounded-2xl flex-shrink-0"
                width={64}
                height={64}
                loading={index < 3 ? "eager" : "lazy"}
                sizes="(max-width: 768px) 48px, 64px"
              />
              <div className="px-5 md:px-0">
                <h3
                  id={`service-title-${index}`}
                  className={`text-[#003449] group-hover:text-white duration-500 ${
                    locale === "ar"
                      ? "zain-regular text-base md:text-lg xl:text-xl font-semibold"
                      : "outfit text-base md:text-lg xl:text-xl font-medium"
                  }`}
                >
                  {t(service.titleKey)}
                </h3>
                <p
                  className={`opacity-60 text-[#1F2126] group-hover:text-white duration-300 mt-1 md:mt-2 ${
                    locale === "ar"
                      ? "zain-regular font-medium text-sm md:text-base xl:text-lg"
                      : "outfit font-normal text-sm md:text-base xl:text-lg"
                  }`}
                >
                  {t(service.descriptionKey)}
                </p>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
