"use client";
import {
  <PERSON>,
  CardContent,
  CardD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FormSchema, validateForm } from "@/lib/FormValidator";
import useSendContact from "@/hooks/useSendContact";
import { toast } from "react-hot-toast";
import Spinner from "./Spinner";
const ContactForm = () => {
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [message, setMessage] = useState("");
  const [errors, setErrors] = useState<Partial<FormSchema>>({});
  const { mutate, isLoading } = useSendContact();

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData: FormSchema = { phone, email, message };

    const validationResult = validateForm(formData);

    if (!validationResult.isValid) {
      setErrors(validationResult.errors || {});
      return;
    }

    setErrors({});

    const formDataObject = new FormData();
    formDataObject.append("email", email);
    formDataObject.append("phone", phone);
    formDataObject.append("message", message);

    mutate(formDataObject, {
      onSuccess: (data) => {
        console.log("Message sent successfully:", data);
        toast.success("we recived your messagge");
        setEmail('')
        setPhone('')
        setMessage('')
      },
      onError: (error: any) => {
        console.error(
          "Error sending message:",

          error.response?.data || error.message
        );
      },
    });
  };
  return (
    <Card className="w-[80%] shadow-lg p-5">
      <CardHeader className="text-left">
        <CardTitle className="text-lg text-[#003449]">Contact Us</CardTitle>
        <CardDescription className="text-md">
          Send us a message and we will be intouch with you soon
        </CardDescription>
      </CardHeader>
      <CardContent className="text-left">
        <form onSubmit={(e) => handleSubmit(e)}>
          <div className="flex w-full md:flex-row-reverse  flex-col-reverse gap-5 justify-between">
            <div className="md:w-[50%] sm:w-full text-[#003449]">
              <Label className="text-md">Phone</Label>
              <Input
                className="text-left"
                type="text"
                placeholder="+123 456 789"
                value={phone}
                onClick={() => setErrors({})}
                onChange={(e) => setPhone(e.target.value)}
              />
              {errors.phone && (
                <p className="text-red-500 text-sm">*{errors.phone}</p>
              )}
            </div>
            <div className="md:w-[50%] sm:w-full text-[#003449]">
              <Label className="text-md">
                Email<span className="text-red-600 text-sm">*</span>
              </Label>
              <Input
                className="text-left text-[#003449]"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onClick={() => setErrors({})}
                onChange={(e) => setEmail(e.target.value)}
              />
              {errors.email && (
                <p className="text-red-600 text-sm">*{errors.email}</p>
              )}
            </div>
          </div>
          <div className="pt-5 text-[#003449]">
            <Label className="text-md">
              Message<span className="text-red-600 text-sm">*</span>
            </Label>
            <Textarea
              placeholder="let's work together..."
              className="h-[100px]"
              value={message}
              onClick={() => setErrors({})}
              onChange={(e) => setMessage(e.target.value)}
            />
            {errors.message && (
              <p className="text-red-600 text-sm">*{errors.message}</p>
            )}
          </div>
          <Button
            type="submit"
            className="bg-[#003449] disabled:bg-[#003449] p-5 my-4 hover:bg-[#4d6194]"
            disabled={isLoading}
          >
            {isLoading ? <Spinner /> : "Submit"}
          </Button>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between"></CardFooter>
    </Card>
  );
};

export default ContactForm;
