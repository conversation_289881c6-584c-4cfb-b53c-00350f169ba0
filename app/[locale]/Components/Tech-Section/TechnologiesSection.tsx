import React from "react";
import dynamic from "next/dynamic";

import Wordpress from "../../../../public/icons/WordPress.svg";
import Angular from "../../../../public/icons/Angular.svg";
import Vue from "../../../../public/icons/Vue.svg";
import nextjs from "../../../../public/icons/nextjs.svg";
import Nuxt from "../../../../public/icons/Nuxt.svg";
import ReactLogo from "../../../../public/icons/React.svg";
import Node from "../../../../public/icons/Nodejs.svg";
import PHP from "../../../../public/icons/Php.svg";
import dotnet from "../../../../public/icons/dotnet.svg";
import NestJS from "../../../../public/icons/nestjs.svg";
import SQL from "../../../../public/icons/mysql.svg";
import NoSQL from "../../../../public/icons/nosql.svg";
import AWS from "../../../../public/icons/AWS.svg";
import GoogleCloud from "../../../../public/icons/googleCloud.svg";
import Azure from "../../../../public/icons/Azure.svg";
import Flutter from "../../../../public/icons/Flutter.svg";
import Swift from "../../../../public/icons/Swift.svg";
import Kotlin from "../../../../public/icons/Kotlin.svg";
const TechnologiesContent = dynamic(() => import("./TechnologiesContent"), {
  ssr: false,
});

interface TechnologiesSectionProps {
  t: (key: string) => string;
  locale: string;
}

const tabs = ["Frontend", "Backend", "Database", "Cloud", "Mobile"];

const TechnologiesSection: React.FC<TechnologiesSectionProps> = ({
  t,
  locale,
}) => {
  const content = {
    Frontend: {
      title: t("technologiesText.frontend.title"),
      description: t("technologiesText.frontend.description"), 
      images: ["WordPress", "Angular", "Vue", "React", "Next.js", "Nuxt.js"],
      CardsImgs: [Wordpress, Angular, Vue, ReactLogo, nextjs, Nuxt],
    },
    Backend: {
      title: t("technologiesText.backend.title"), 
      description: t("technologiesText.backend.description"), 
      images: ["Node", "PHP", ".Net", "NestJS"],
      CardsImgs: [Node, PHP, dotnet, NestJS],
    },
    Database: {
      title: t("technologiesText.database.title"), 
      description: t("technologiesText.database.description"),
      images: ["SQL", "NoSQL"],
      CardsImgs: [SQL, NoSQL],
    },
    Cloud: {
      title: t("technologiesText.cloud.title"),
      description: t("technologiesText.cloud.description"), 
      images: ["AWS", "Google Cloud", "Azure"],
      CardsImgs: [AWS, GoogleCloud, Azure],
    },
    Mobile: {
      title: t("technologiesText.mobile.title"), 
      description: t("technologiesText.mobile.description"),
      images: ["Flutter", "Swift", "Kotlin"],
      CardsImgs: [Flutter, Swift, Kotlin],
    },
  };

  return (
    <main className="w-full relative min-h-screen overflow-hidden flex justify-center text-black mt-[12rem] xl:h-auto" id="technologies">
     
      <section className="w-[100%] xl:w-[80%] xl:rounded-3xl relative z-[1]">
     
        <div className="w-full flex justify-center">
          {/* Text */}
          <div className="text-center" dir={locale === "ar" ? "rtl" : ""}>
            <h2
              className={`font-medium text-[#005171] ${
                locale === "ar"
                  ? "zain-regular text-[2.6rem] lg:text-[3.6rem]"
                  : "outfit text-[2.6rem] lg:text-[3.6rem]"
              }`}
            >
              {t("technologiesText.sectionTitle")}
            </h2>
            <p
              className={`text-[#005171] px-[2rem] lg:px-[7rem] ${
                locale === "ar"
                  ? "zain-regular text-[1.4rem] xl:text-[1.5rem]"
                  : "outfit text-[1.4rem] xl:text-[1.5rem]"
              }`}
              style={{ textShadow: "0 0 8px rgba(255, 255, 255, 0.4)" }}
            >
              {t("technologiesText.mainTitle")}
            </p>
          </div>
        </div>

        {/* Dynamic TechnologiesContent component */}
        <TechnologiesContent tabs={tabs} content={content} locale={locale} />
      </section>
    </main>
  );
};

export default TechnologiesSection;
