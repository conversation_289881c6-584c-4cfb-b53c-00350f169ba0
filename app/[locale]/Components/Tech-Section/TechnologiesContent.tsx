"use client";

import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { motion } from "framer-motion";

const cn = (...classes: (string | undefined)[]) =>
  classes.filter(Boolean).join(" ");

type Content = {
  title: string;
  description: string;
  images: string[];
  CardsImgs: any[];
};

interface TechnologiesContentProps {
  tabs: string[];
  content: Record<string, Content>;
  locale: string;
}

const TechnologiesContent: React.FC<TechnologiesContentProps> = ({
  tabs,
  content,
  locale,
}) => {
  const [activeTab, setActiveTab] = useState<string>(tabs[0]);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  const indicatorRef = useRef<HTMLDivElement>(null);
  const tabsRef = useRef<(HTMLLIElement | null)[]>([]);

  const updateIndicatorPosition = () => {
    const activeIndex = tabs.indexOf(activeTab);
    const currentTab = tabsRef.current[activeIndex];

    if (currentTab && indicatorRef.current) {
      const tabRect = currentTab.getBoundingClientRect();
      const containerRect = currentTab.parentElement?.getBoundingClientRect();

      if (tabRect && containerRect) {
        const leftPosition =
          tabRect.left - containerRect.left + tabRect.width / 2;
        indicatorRef.current.style.transform = `translateX(${leftPosition}px) translateX(-50%)`;
      }
    }
  };

  useEffect(() => {
    updateIndicatorPosition();
    window.addEventListener("resize", updateIndicatorPosition);
    return () => window.removeEventListener("resize", updateIndicatorPosition);
  }, [activeTab]);

  const handleTabClick = (tab: string) => {
    if (tab !== activeTab) {
      setIsAnimating(true);
      setTimeout(() => {
        setActiveTab(tab);
        setIsAnimating(false);
      }, 300);
    }
  };

  const variants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
  };

  return (
    <section className="flex h-full flex-col w-full mt-6">
      <div className="w-full flex flex-col items-center relative mb-3 z-[50]">
        <div className="relative w-full">
          {/* Indicator */}
          <div
            ref={indicatorRef}
            className="absolute top-0 w-[1rem] h-[1rem] bg-[#b5e7a7] rounded-full transition-transform duration-300 ease-in-out z-[10]"
            style={{ transform: "translateX(-50%)" }}
          ></div>
        </div>
        {/* Line */}
        <div className="absolute w-full h-[0.2rem] bg-gradient-to-r from-[#84ad4d]  via-[#005171] to-100% top-[0.4rem]"></div>

        {/* Tabs */}
        <ul className="flex gap-[1rem] my-[2rem] w-full justify-around px-[1rem] uppercase">
          {tabs.map((tab, index) => (
            <li
              key={tab}
              ref={(el) => {
                tabsRef.current[index] = el;
              }}
              className={`relative cursor-pointer text-[0.75rem] sm:text-[1rem] text-[#005171] font-semibold transition-opacity duration-250 text-center ${
                activeTab === tab ? "opacity-100" : "opacity-40"
              }`}
              onClick={() => handleTabClick(tab)}
            >
              {tab}
            </li>
          ))}
        </ul>
      </div>

      <div className="w-full flex flex-col lg:flex-row justify-center items-center"   dir={locale === "ar" ? "rtl" : ""}>
        {/* Content Card */}
        <div className="relative w-[90%] h-full " >
          <div
            className="h-full w-full relative rounded-2xl overflow-hidden flex flex-col justify-start items-center text-center lg:text-start text-[#005171] lg:leading-[3rem] pt-[2rem] lg:pt-[4rem] "
          
          >
            <motion.div
              className="w-[90%] py-[2rem] lg:py-[0rem]  "
              initial="hidden"
              animate={isAnimating ? "hidden" : "visible"}
              variants={variants}
              transition={{ duration: 0.3 }}
              dir={locale === "ar" ? "rtl" : ""}
            >
              <h2
                className={`font-bold pb-[1rem] ${
                  locale === "ar"
                    ? "zain-regular text-[1.3rem] lg:text-[2.4rem] "
                    : "montserrat text-[1.3rem] lg:text-[2.4rem] "
                }`}
              >
                {content[activeTab].title}
              </h2>
              <p
                className={`lg:max-w-[70%] text-[1.2rem] lg:text-[1.8rem]  ${
                  locale === "ar" ? "zain-regular" : "montserrat "
                }`}
              >
                {content[activeTab].description}
              </p>
            </motion.div>
          </div>
        </div>

        {/* Technology Grid */}
        <div
          className={` w-[90%] lg:w-[70%] relative mb-4 transition-opacity duration-300 ${
            isAnimating ? "opacity-0" : "opacity-100"
          }`}
        >
          <div className="relative h-full scrollable-grid custom-scrollbar">
            <div className="grid grid-cols-3 gap-6 h-full gap-x-0 ">
              {content[activeTab].CardsImgs.map((imgSrc, index) => (
                <div
                  key={index}
                  className="bg-[#fbfbfb] hover:bg-[#c0c0c0] shadow-xl w-[90%] h-[12rem] md:h-[15rem]  flex items-center justify-center rounded-2xl transition-all duration-300 relative z-[1]"
                >
                  <Image
                    src={imgSrc}
                    alt={content[activeTab].images[index]}
                    priority={true}
                    className="absolute z-[-1] w-[80%] md:w-[70%] top-4"
                  />
                  <div
                    className={`absolute uppercase montserrat font-medium text-[1.2rem] bottom-[2rem] text-[#33748D] ${
                      isAnimating ? "opacity-0" : "opacity-100"
                    }`}
                  >
                    {content[activeTab].images[index]}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TechnologiesContent;
