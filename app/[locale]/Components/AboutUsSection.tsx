import Image from "next/image";
import React from "react";

interface AboutUsSectionProps {
  t: (key: string) => string;
  locale: string;
}

const AboutUsSection: React.FC<AboutUsSectionProps> = ({ t, locale }) => {
  return (
    <main className="w-full relative min-h-screen flex flex-col justify-center items-center mt-[4rem]">
      <section className="w-full text-center">
        <h2
          className={`text-center text-[2.5rem] sm:text-[4rem] md:text-[5rem] lg:text-[6.2rem] mb-8 sm:mb-12 lg:mb-16 ${
            locale === "ar" ? "zain-regular" : "outfit"
          }`}
          dir={locale === "ar" ? "rtl" : ""}
        >
          <span className="text-[#003449]">{t("aboutUs")} </span>
          <span className="inline-block bg-gradient-to-r from-[#6aad38] to-[#003449] text-transparent bg-clip-text">
            {t("aboutUs2")}{" "}
          </span>
        </h2>
      </section>

      <section
        className="w-[90%] lg:w-[100%] flex flex-col lg:flex-row justify-center items-center py-[1rem] sm:py-[1.5rem] lg:py-[2rem] gap-[2rem] sm:gap-[3rem] lg:gap-[8rem] "
        dir={locale === "ar" ? "rtl" : ""}
      >
        {/* Welcome Text - Left Aligned */}
        <div className="max-w-[100%] lg:max-w-[35rem] text-[#005171]">
          <p
            className={`${
              locale === "ar"
                ? "zain-regular text-[1.1rem] sm:text-[1.2rem] lg:text-[1.5rem] font-medium leading-[2.5rem] sm:leading-[2.8rem] lg:leading-[3.4rem]"
                : "montserrat text-[1rem] sm:text-[1.15rem] lg:text-[1.3rem] font-medium leading-[2.2rem] sm:leading-[2.6rem] lg:leading-[3rem]"
            } text-[#11445f]`}
          >
            {t("aboutUsDescription")}
          </p>
          <div className="flex items-start mt-6 gap-2">
            <Image src='/icons/disclaimer.svg' width={20} height={25} alt="notice icon"/>
            <p
              className={`${
                locale === "ar"
                  ? "zain-regular text-[0.8rem] sm:text-[0.9rem]  font-medium  "
                  : "montserrat text-[0.8rem] sm:text-[0.9rem]  font-medium leading-[1.5rem] "
              } text-[#11445f]`}
            >
              {t("notice.title")}
            </p>
          </div>
        </div>
        {/* Mission and Language Sections */}
        <div className="flex flex-col space-y-[4rem]">
          <article>
            <h3
              className={`text-[#005171] font-semibold mb-4 ${
                locale === "ar"
                  ? "zain-regular text-[2rem]"
                  : "outfit text-[2rem]"
              }`}
            >
              {t("missionStatement.title")}
            </h3>
            <p
              className={`text-[#005171] max-w-[30rem] uppercase ${
                locale === "ar"
                  ? "zain-regular text-[1.3rem] font-medium"
                  : "montserrat text-[1.3rem] font-medium"
              }`}
            >
              {t("missionStatement.description")}
            </p>
          </article>

          <article>
            <h3
              className={`text-[#005171] font-semibold mb-4 ${
                locale === "ar"
                  ? "zain-regular text-[2rem]"
                  : "outfit text-[2rem]"
              }`}
            >
              {t("languageSupport.title")}
            </h3>
            <p
              className={`text-[#005171] max-w-[30rem] uppercase ${
                locale === "ar"
                  ? "zain-regular text-[1.3rem] font-medium"
                  : "montserrat text-[1.3rem] font-medium"
              }`}
            >
              {t("languageSupport.description")}
            </p>
          </article>
        </div>
      </section>
    </main>
  );
};

export default AboutUsSection;
