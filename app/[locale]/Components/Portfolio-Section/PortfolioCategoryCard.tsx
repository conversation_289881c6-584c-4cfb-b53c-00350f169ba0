"use client";

import React, { useState } from "react";
import Image from "next/image";
import { LocalizedPortfolioCategory } from "./portfolioData";

interface PortfolioCategoryCardProps {
  category: LocalizedPortfolioCategory;
  locale: string;
  translations: {
    techStack: string;
    keyFeatures: string;
  };
  index: number;
}

const PortfolioCategoryCard: React.FC<PortfolioCategoryCardProps> = ({
  category,
  locale,
  translations,
  index,
}) => {
  const isArabic = locale === "ar";
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isExpanded, setIsExpanded] = useState(false);

  const nextImage = () => {
    setCurrentImageIndex((prev) =>
      prev === category.screenshots.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) =>
      prev === 0 ? category.screenshots.length - 1 : prev - 1
    );
  };

  const goToImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Truncate description to approximately 150 characters
  const truncatedDescription = category.categoryDescription && category.categoryDescription.length > 150
    ? category.categoryDescription.substring(0, 150) + "..."
    : category.categoryDescription || "";

  return (
    <article
      className="group bg-white rounded-xl border border-gray-100 hover:border-gray-200 transition-all duration-300 overflow-hidden"
      role="listitem"
      aria-labelledby={`category-title-${index}`}
    >
      {/* Image Section */}
      <div className="relative h-64 bg-gray-50 overflow-hidden group">
        <Image
          src={category.screenshots[currentImageIndex]}
          alt={`${category.category} screenshot ${currentImageIndex + 1}`}
          fill
          className="object-contain p-4"
          loading={index < 2 ? "eager" : "lazy"}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />

        {/* Navigation Arrows */}
        {category.screenshots.length > 1 && (
          <>
            <button
              onClick={prevImage}
              className={`absolute top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-300 opacity-0 group-hover:opacity-100 ${
                isArabic ? "right-3" : "left-3"
              }`}
              aria-label="Previous image"
            >
              <svg
                className={`w-4 h-4 ${isArabic ? "rotate-180" : ""}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            <button
              onClick={nextImage}
              className={`absolute top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-300 opacity-0 group-hover:opacity-100 ${
                isArabic ? "left-3" : "right-3"
              }`}
              aria-label="Next image"
            >
              <svg
                className={`w-4 h-4 ${isArabic ? "rotate-180" : ""}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </>
        )}

        {/* Simple Dots Navigation */}
        {category.screenshots.length > 1 && (
          <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {category.screenshots.map((_, imgIndex) => (
              <button
                key={imgIndex}
                onClick={() => goToImage(imgIndex)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  imgIndex === currentImageIndex
                    ? "bg-[#005171]"
                    : "bg-gray-300 hover:bg-gray-400"
                }`}
                aria-label={`Go to image ${imgIndex + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Content Section */}
      <div className="p-6">
        <h3
          id={`category-title-${index}`}
          className={`text-[#005171] font-semibold mb-3 ${
            isArabic
              ? "zain-regular text-xl"
              : "outfit text-xl"
          }`}
          dir={isArabic ? "rtl" : "ltr"}
        >
          {category.category}
        </h3>

        <p
          className={`text-gray-600 leading-relaxed mb-4 ${
            isArabic
              ? "zain-regular text-sm"
              : "outfit text-sm"
          }`}
          dir={isArabic ? "rtl" : "ltr"}
        >
          {isExpanded ? category.categoryDescription : truncatedDescription}
        </p>

        {category.categoryDescription && category.categoryDescription.length > 150 && (
          <button
            onClick={toggleExpanded}
            className={`text-[#005171] hover:text-[#71b331] transition-colors duration-300 text-xs mb-4 ${
              isArabic
                ? "zain-regular"
                : "outfit"
            }`}
            dir={isArabic ? "rtl" : "ltr"}
          >
            {isExpanded ? (isArabic ? "أقل" : "Less") : (isArabic ? "المزيد" : "More")}
          </button>
        )}

        {/* Key Features */}
        <div className="mb-4" dir={isArabic ? "rtl" : "ltr"}>
          <h4
            className={`text-[#005171] font-medium mb-2 text-sm ${
              isArabic ? "zain-regular text-right" : "outfit text-left"
            }`}
          >
            {translations.keyFeatures}
          </h4>
          <ul className={`space-y-1 ${isArabic ? "text-right" : "text-left"}`}>
            {category.keyFeatures.slice(0, 3).map((feature, featureIndex) => (
              <li
                key={featureIndex}
                className={`flex items-center text-gray-600 text-xs ${
                  isArabic
                    ? "zain-regular justify-end"
                    : "outfit justify-start"
                }`}
              >
                <div className={`w-1.5 h-1.5 bg-[#71b331] rounded-full flex-shrink-0 ${isArabic ? "ml-2 order-2" : "mr-2 order-1"}`}></div>
                <span className={isArabic ? "order-1" : "order-2"}>{feature}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Tech Stack */}
        <div className={`flex flex-wrap gap-2 ${isArabic ? "flex-row-reverse" : ""}`}>
          {category.techStack.slice(0, 3).map((tech, techIndex) => (
            <span
              key={techIndex}
              className={`px-3 py-1 text-xs text-gray-500 bg-gray-100 rounded-full ${
                isArabic ? "zain-regular" : "outfit"
              }`}
            >
              {tech}
            </span>
          ))}
          {category.techStack.length > 3 && (
            <span className={`px-3 py-1 text-xs text-gray-400 bg-gray-50 rounded-full ${isArabic ? "zain-regular" : "outfit"}`}>
              +{category.techStack.length - 3}
            </span>
          )}
        </div>
      </div>
    </article>
  );
};

export default PortfolioCategoryCard;