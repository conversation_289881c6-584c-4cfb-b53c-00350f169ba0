import React from "react";
import Image from "next/image";
import { getLocalizedPortfolioCategories } from "./portfolioData";
import PortfolioCategoryCardWrapper from "./PortfolioCategoryCardWrapper";

interface PortfolioGridProps {
  t: (key: string) => string;
  locale: string;
}

const PortfolioGrid: React.FC<PortfolioGridProps> = ({ t, locale }) => {
  const isArabic = locale === "ar";

  // Get localized portfolio categories
  const localizedCategories = getLocalizedPortfolioCategories(t, locale);

  return (
    <div className="w-full px-4 md:px-8 xl:w-4/5">
      {/* Categories Grid */}
      <div
        className="w-full grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8"
        role="list"
        aria-label="Portfolio categories"
      >
        {localizedCategories.map((category, index) => (
          <PortfolioCategoryCardWrapper
            key={category.id}
            category={category}
            locale={locale}
            t={t}
            index={index}
          />
        ))}
      </div>

      {/* Additional Info Section */}
      <div className="mt-16 text-center">
        <p
          className={`text-[#58626c] opacity-80 font-normal w-full md:w-4/5 xl:w-3/4 mx-auto leading-relaxed ${
            isArabic
              ? "zain-regular text-[1.2rem] xl:text-[1.6rem]"
              : "outfit text-[1.2rem] xl:text-[1.6rem]"
          }`}
          dir={isArabic ? "rtl" : "ltr"}
        >
          {t("portfolioText.additionalInfo")}
        </p>
      </div>
    </div>
  );
};

export default PortfolioGrid;
