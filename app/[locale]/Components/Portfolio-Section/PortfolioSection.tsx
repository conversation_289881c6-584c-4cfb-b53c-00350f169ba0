import React from "react";
import Image from "next/image";
import Link from "next/link";
import PortfolioCategoryCardWrapper from "./PortfolioCategoryCardWrapper";
import { getLocalizedPortfolioCategories } from "./portfolioData";

interface PortfolioSectionProps {
  t: (key: string) => string;
  locale: string;
}

const PortfolioSection: React.FC<PortfolioSectionProps> = ({ t, locale }) => {
  const isArabic = locale === "ar";

  // Get localized portfolio categories
  const localizedCategories = getLocalizedPortfolioCategories(t, locale);
  // Featured categories for the main page (showing 3 categories)
  const featuredCategories = localizedCategories.slice(0, 3);

  return (
    <section
      className="w-full relative flex items-center justify-center flex-col mt-8 mb-20 md:mt-12 xl:mt-16"
      aria-labelledby="portfolio-title"
      dir={isArabic ? "rtl" : "ltr"}
    >
      <div className="w-full px-4 md:px-8 xl:w-4/5 flex flex-col items-center justify-center">
        {/* Section Header */}
        <h2
          id="portfolio-title"
          className={`uppercase text-[#005171] pb-4 ${
            isArabic
              ? "zain-regular font-semibold text-[2.8rem] xl:text-[4.2rem]"
              : "outfit font-medium text-[2.8rem] xl:text-[4.2rem]"
          }`}
        >
          {t("portfolioText.sectionTitle")}
        </h2>
        
        <p
          className={`text-[#58626c] font-normal w-full md:w-4/5 xl:w-2/3 leading-relaxed md:leading-loose xl:leading-[4rem] ${
            isArabic
              ? "zain-regular text-[1.8rem] xl:text-[2.8rem]"
              : "outfit text-[1.8rem] xl:text-[2.8rem]"
          } text-center mb-4`}
        >
          {t("portfolioText.sectionSubtitle")}
        </p>

        <p
          className={`text-[#58626c] opacity-80 font-normal w-full md:w-4/5 xl:w-3/4 leading-relaxed ${
            isArabic
              ? "zain-regular text-[1.2rem] xl:text-[1.6rem]"
              : "outfit text-[1.2rem] xl:text-[1.6rem]"
          } text-center mb-8`}
        >
          {t("portfolioText.sectionDescription")}
        </p>

        {/* Featured Categories Grid */}
        <div
          className="w-full grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 md:gap-8 mb-8"
          role="list"
          aria-label="Featured portfolio categories"
        >
          {featuredCategories.map((category, index) => (
            <PortfolioCategoryCardWrapper
              key={category.id}
              category={category}
              locale={locale}
              t={t}
              index={index}
            />
          ))}
        </div>

        {/* View All Portfolio Button */}
        <div className="flex justify-center mt-8">
          <Link
            href={`/${locale}/portfolio`}
            className={`group px-[2rem] lg:px-[3rem] py-[1rem] lg:py-[1.2rem] rounded-full bg-[#005274] hover:bg-[#003449] transition-all duration-300 flex items-center space-x-[1rem] lg:space-x-[2rem] ${
              isArabic ? "flex-row-reverse space-x-reverse" : ""
            }`}
          >
            <span
              className={`text-white font-extrabold flex items-center whitespace-nowrap ${
                isArabic
                  ? "zain-regular tracking-[0.04rem] text-[1.2rem] lg:text-[1.6rem] leading-[1.8rem] lg:leading-[2.4rem]"
                  : "outfit tracking-[0.15rem] text-[0.9rem] lg:text-[1.3rem] leading-[1.35rem] lg:leading-[1.95rem]"
              }`}
            >
              {t("portfolioText.viewAllPortfolio")}
            </span>
            <svg
              className={`w-5 h-5 lg:w-6 lg:h-6 text-white group-hover:translate-x-1 transition-transform duration-300 ${
                isArabic ? "rotate-180" : ""
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;
