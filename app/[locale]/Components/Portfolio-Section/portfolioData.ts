// Portfolio data structure based on Google Sheets content
export interface PortfolioCategory {
  id: string;
  screenshots: string[];
}

// Localized portfolio category interface
export interface LocalizedPortfolioCategory {
  id: string;
  category: string;
  categoryDescription: string;
  screenshots: string[];
  techStack: string[];
  keyFeatures: string[];
  projectExample?: {
    name: string;
    shortDescription: string;
  };
}

// Helper function to get localized portfolio data
export const getLocalizedPortfolioCategories = (t: (key: string) => any, locale: string): LocalizedPortfolioCategory[] => {
  // Define the data in both languages
  const portfolioData = {
    en: {
      healthcare: {
        name: "Healthcare",
        description: "We deliver smart, scalable digital solutions tailored for healthcare providers. From streamlined appointment booking systems and medical center websites to patient engagement tools and team management platforms, our services are designed to enhance operational efficiency, improve patient experience, and support the digital transformation of clinics, hospitals, and wellness centers.",
        techStack: ["Next.js", "Bootstrap", "Node.js", "MongoDB"],
        keyFeatures: ["Services section", "Team profiles", "Patient testimonials", "Responsive design", "Contact and location info", "Appointment booking section", "SEO optimization"],
        projectExample: {
          name: "Al-ain Medical Center",
          description: "A modern, user-friendly website for a medical center that provides a comprehensive overview of the clinic, details of offered medical services, professional team profiles, patient testimonials, and an integrated appointment booking system."
        }
      },
      ecommerce: {
        name: "E-Commerce",
        description: "We build powerful, customizable e-commerce platforms designed to streamline online selling and delivery operations. From fast deployment and multi-language support to inventory management, payment automation, and marketing tools—our solutions help businesses launch, grow, and scale with confidence.",
        techStack: ["Angular", "Flutter", "Node.js", "PostgreSQL"],
        keyFeatures: ["Rapid Deployment", "Unified Operations Dashboard", "Built-in Growth Suite", "Multi-Market Ready", "End-to-End Automation", "Mobile-First Experience", "Secure & Compliant"],
        projectExample: {
          name: "Maradi",
          description: "A comprehensive e-commerce and delivery management platform designed to help businesses launch and scale quickly. With a focus on rapid app development, integrated operations, and built-in growth tools like SEO and analytics."
        }
      },
      shipment: {
        name: "Shipment & Distribution",
        description: "We provide innovative, community-driven delivery platforms that connect senders with travelers for faster, more affordable, and secure parcel shipping worldwide. Our solutions offer flexible shipment options, including VIP express services and regular peer-to-peer deliveries, combined with optional indemnity coverage and real-time tracking.",
        techStack: ["React Native", "Node.js", "MongoDB", "Socket.io"],
        keyFeatures: ["VIP & Emergency Shipments", "Regular Shipments with Travelers", "Mobile App with Smart Matching", "Real-Time Notifications & Tracking", "Traveler Rewards System"],
        projectExample: {
          name: "Paquik",
          description: "A peer-to-peer delivery platform that connects senders with travelers to ship items faster, cheaper, and more socially than traditional courier services."
        }
      },
      epayment: {
        name: "E-Payment",
        description: "We deliver seamless, secure digital payment platforms that simplify financial management across multiple banks and accounts. Our solutions enable instant balance tracking, effortless fund transfers using just mobile numbers, and an intuitive user experience—eliminating the need for complex banking details.",
        techStack: ["Flutter", "Node.js", "PostgreSQL", "Redis"],
        keyFeatures: ["Multi-Bank Account Management", "Real-Time Balance Updates", "Mobile Number Transfers", "Seamless & Secure Transfers"],
        projectExample: {
          name: "Salim Pay",
          description: "A free all-in-one banking app that lets users link multiple bank accounts, check balances in real time, and make seamless transfers using just a mobile number—no IBANs needed."
        }
      }
    },
    ar: {
      healthcare: {
        name: "الرعاية الصحية",
        description: "نقدم حلولاً رقمية ذكية وقابلة للتوسع مصممة خصيصاً لمقدمي الرعاية الصحية. من أنظمة حجز المواعيد المبسطة ومواقع المراكز الطبية إلى أدوات إشراك المرضى ومنصات إدارة الفرق، خدماتنا مصممة لتعزيز الكفاءة التشغيلية وتحسين تجربة المرضى ودعم التحول الرقمي للعيادات والمستشفيات ومراكز العافية.",
        techStack: ["Next.js", "Bootstrap", "Node.js", "MongoDB"],
        keyFeatures: ["قسم الخدمات", "ملفات الفريق", "شهادات المرضى", "تصميم متجاوب", "معلومات الاتصال والموقع", "قسم حجز المواعيد", "تحسين محركات البحث"],
        projectExample: {
          name: "مركز العين الطبي",
          description: "موقع ويب حديث وسهل الاستخدام لمركز طبي يوفر نظرة شاملة على العيادة وتفاصيل الخدمات الطبية المقدمة وملفات الفريق المهني وشهادات المرضى ونظام حجز مواعيد متكامل."
        }
      },
      ecommerce: {
        name: "التجارة الإلكترونية",
        description: "نبني منصات تجارة إلكترونية قوية وقابلة للتخصيص مصممة لتبسيط عمليات البيع والتوصيل عبر الإنترنت. من النشر السريع والدعم متعدد اللغات إلى إدارة المخزون وأتمتة المدفوعات وأدوات التسويق - تساعد حلولنا الشركات على الإطلاق والنمو والتوسع بثقة.",
        techStack: ["Angular", "Flutter", "Node.js", "PostgreSQL"],
        keyFeatures: ["النشر السريع", "لوحة تحكم العمليات الموحدة", "مجموعة النمو المدمجة", "جاهز لعدة أسواق", "الأتمتة الشاملة", "تجربة الهاتف المحمول أولاً", "آمن ومتوافق"],
        projectExample: {
          name: "مرادي",
          description: "منصة شاملة للتجارة الإلكترونية وإدارة التوصيل مصممة لمساعدة الشركات على الإطلاق والتوسع بسرعة. مع التركيز على تطوير التطبيقات السريع والعمليات المتكاملة وأدوات النمو المدمجة مثل تحسين محركات البحث والتحليلات."
        }
      },
      shipment: {
        name: "الشحن والتوزيع",
        description: "نوفر منصات توصيل مبتكرة مدفوعة بالمجتمع تربط المرسلين بالمسافرين لشحن الطرود بشكل أسرع وأكثر اقتصادية وأماناً في جميع أنحاء العالم. تقدم حلولنا خيارات شحن مرنة، بما في ذلك خدمات VIP السريعة والتوصيل العادي من نظير إلى نظير، مع تغطية تعويضية اختيارية وتتبع في الوقت الفعلي.",
        techStack: ["React Native", "Node.js", "MongoDB", "Socket.io"],
        keyFeatures: ["شحنات VIP والطوارئ", "الشحنات العادية مع المسافرين", "تطبيق الهاتف المحمول مع المطابقة الذكية", "الإشعارات والتتبع في الوقت الفعلي", "نظام مكافآت المسافرين"],
        projectExample: {
          name: "باكيك",
          description: "منصة توصيل من نظير إلى نظير تربط المرسلين بالمسافرين لشحن العناصر بشكل أسرع وأرخص وأكثر اجتماعية من خدمات البريد السريع التقليدية."
        }
      },
      epayment: {
        name: "الدفع الإلكتروني",
        description: "نقدم منصات دفع رقمية سلسة وآمنة تبسط الإدارة المالية عبر عدة بنوك وحسابات. تمكن حلولنا من تتبع الرصيد الفوري وتحويل الأموال بسهولة باستخدام أرقام الهواتف المحمولة فقط وتجربة مستخدم بديهية - مما يلغي الحاجة إلى تفاصيل مصرفية معقدة.",
        techStack: ["Flutter", "Node.js", "PostgreSQL", "Redis"],
        keyFeatures: ["إدارة حسابات متعددة البنوك", "تحديثات الرصيد في الوقت الفعلي", "تحويلات برقم الهاتف المحمول", "تحويلات سلسة وآمنة"],
        projectExample: {
          name: "سليم باي",
          description: "تطبيق مصرفي مجاني شامل يتيح للمستخدمين ربط حسابات مصرفية متعددة والتحقق من الأرصدة في الوقت الفعلي وإجراء تحويلات سلسة باستخدام رقم الهاتف المحمول فقط - دون الحاجة إلى أرقام IBAN."
        }
      }
    }
  };

  const currentLocale = locale === 'ar' ? 'ar' : 'en';
  const data = portfolioData[currentLocale];

  return portfolioCategories.map(category => {
    const categoryData = data[category.id as keyof typeof data];

    return {
      id: category.id,
      category: categoryData?.name || category.id,
      categoryDescription: categoryData?.description || "",
      screenshots: category.screenshots,
      techStack: categoryData?.techStack || [],
      keyFeatures: categoryData?.keyFeatures || [],
      projectExample: categoryData?.projectExample ? {
        name: categoryData.projectExample.name,
        shortDescription: categoryData.projectExample.description
      } : undefined
    };
  });
};

export const portfolioCategories: PortfolioCategory[] = [
  {
    id: "healthcare",
    screenshots: [
      "/portfolio/healthcare/01.png",
      "/portfolio/healthcare/02.png",
      "/portfolio/healthcare/03.png",
      "/portfolio/healthcare/04.png",
      "/portfolio/healthcare/05.png",
    ]
  },
  {
    id: "ecommerce",
    screenshots: [
      "/portfolio/e-commerce/01.webp",
      "/portfolio/e-commerce/02.webp",
      "/portfolio/e-commerce/03.webp",
      "/portfolio/e-commerce/04.webp",
      "/portfolio/e-commerce/05.webp",
      "/portfolio/e-commerce/06.webp",
    ]
  },
  {
    id: "shipment",
    screenshots: [
      "/portfolio/shipment/01.webp",
      "/portfolio/shipment/02.webp",
      "/portfolio/shipment/03.webp",
      "/portfolio/shipment/04.webp",
    ]
  },
  {
    id: "epayment",
    screenshots: [
      "/portfolio/payment/01.webp",
      "/portfolio/payment/02.webp",
      "/portfolio/payment/03.webp",
      "/portfolio/payment/04.webp",
      "/portfolio/payment/05.webp",
    ]
  }
];
