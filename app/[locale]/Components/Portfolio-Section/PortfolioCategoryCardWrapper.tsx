import React from "react";
import PortfolioCategoryCard from "./PortfolioCategoryCard";
import { LocalizedPortfolioCategory } from "./portfolioData";

interface PortfolioCategoryCardWrapperProps {
  category: LocalizedPortfolioCategory;
  locale: string;
  t: (key: string) => string;
  index: number;
}

const PortfolioCategoryCardWrapper: React.FC<PortfolioCategoryCardWrapperProps> = ({
  category,
  locale,
  t,
  index,
}) => {
  const translations = {
    techStack: t("portfolioText.techStack"),
    keyFeatures: t("portfolioText.keyFeatures"),
  };

  return (
    <PortfolioCategoryCard
      category={category}
      locale={locale}
      translations={translations}
      index={index}
    />
  );
};

export default PortfolioCategoryCardWrapper;
