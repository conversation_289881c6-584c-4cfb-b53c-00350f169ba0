import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";

interface Project {
  id: string;
  title: string;
  description: string;
  category: string;
  image: string;
  technologies: string[];
  featured?: boolean;
}

interface ProjectCardProps {
  project: Project;
  locale: string;
  t: (key: string) => string;
  index: number;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project, locale, t, index }) => {
  const isArabic = locale === "ar";
  const [isExpanded, setIsExpanded] = useState(false);

  // Check if description is long enough to need truncation
  const isLongDescription = project.description.length > 150;

  return (
    <article
      className="group bg-white rounded-xl md:rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2"
      role="listitem"
      aria-labelledby={`project-title-${index}`}
    >
      {/* Project Image */}
      <div className="relative h-48 md:h-56 overflow-hidden bg-gray-100 flex items-center justify-center">
        <Image
          src={project.image}
          alt={`${project.title} - ${project.category}`}
          fill
          className="object-contain group-hover:scale-110 transition-transform duration-500"
          loading={index < 3 ? "eager" : "lazy"}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />

        {/* Category Badge */}
        <div className="absolute top-4 left-4 bg-[#005171] text-white px-3 py-1 rounded-full">
          <span
            className={`text-sm font-medium ${isArabic ? "zain-regular" : "outfit"
              }`}
          >
            {project.category}
          </span>
        </div>

        {/* Overlay on hover */}
        <div className="absolute inset-0 bg-[#003449] bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-500 flex items-center justify-center">
          <Link
            href={`/${locale}/portfolio/${project.id}`}
            className="opacity-0 group-hover:opacity-100 transition-opacity  bg-white text-[#003449] px-6 py-3 rounded-full font-bold hover:bg-[#71b331] hover:text-white transform translate-y-4 group-hover:translate-y-0 duration-500"
          >
            <span
              className={`${isArabic ? "zain-regular text-sm" : "outfit text-sm"
                }`}
            >
              {t("portfolioText.viewProject")}
            </span>
          </Link>
        </div>
      </div>

      {/* Project Content */}
      <div className="p-6">
        <h3
          id={`project-title-${index}`}
          className={`text-[#003449] mb-3 ${isArabic
              ? "zain-regular text-lg md:text-xl font-semibold"
              : "outfit text-lg md:text-xl font-medium"
            }`}
        >
          {project.title}
        </h3>

        <div className="mb-4">
          <p
            className={`text-[#58626c] ${isArabic
                ? "zain-regular text-sm md:text-base leading-relaxed"
                : "outfit text-sm md:text-base leading-relaxed"
              }`}
          >
            {isLongDescription && !isExpanded
              ? (
                <>
                  {project.description.substring(0, 150)}...{" "}
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className={`text-[#005171] hover:text-[#003449] font-medium transition-colors duration-200 ${
                      isArabic ? "zain-regular" : "outfit"
                    }`}
                  >
                    {isArabic ? "عرض المزيد" : "show more"}
                  </button>
                </>
              )
              : (
                <>
                  {project.description}{" "}
                  {isLongDescription && (
                    <button
                      onClick={() => setIsExpanded(!isExpanded)}
                      className={`text-[#005171] hover:text-[#003449] font-medium transition-colors duration-200 ${
                        isArabic ? "zain-regular" : "outfit"
                      }`}
                    >
                      {isArabic ? "عرض أقل" : "show less"}
                    </button>
                  )}
                </>
              )
            }
          </p>
        </div>

        {/* Technologies */}
        <div className="mb-4" dir={isArabic ? "rtl" : "ltr"}>
          <h4
            className={`text-[#003449] mb-2 ${isArabic
                ? "zain-regular text-sm font-semibold text-right"
                : "outfit text-sm font-medium text-left"
              }`}
          >
            {t("portfolioText.technologies")}:
          </h4>
          <div className={`flex flex-wrap gap-2 ${isArabic ? "flex-row-reverse justify-start" : "justify-start"}`}>
            {project.technologies.slice(0, 3).map((tech, techIndex) => (
              <span
                key={techIndex}
                className={`bg-[#F1FFE9] text-[#005171] px-2 py-1 rounded-md text-xs ${isArabic ? "zain-regular" : "outfit"
                  }`}
              >
                {tech}
              </span>
            ))}
            {project.technologies.length > 3 && (
              <span
                className={`bg-[#e7e7e7] text-[#58626c] px-2 py-1 rounded-md text-xs ${isArabic ? "zain-regular" : "outfit"
                  }`}
              >
                +{project.technologies.length - 3}
              </span>
            )}
          </div>
        </div>

        {/* View Project Link */}
        <Link
          href={`/${locale}/portfolio/${project.id}`}
          className={`inline-flex items-center text-[#005171] hover:text-[#003449] font-medium transition-colors duration-300 ${isArabic
              ? "zain-regular text-sm flex-row-reverse"
              : "outfit text-sm"
            }`}
        >
          <span>{t("portfolioText.viewProject")}</span>
          <svg
            className={`w-4 h-4 transition-transform duration-300 group-hover:translate-x-1 ${isArabic ? "mr-2 rotate-180" : "ml-2"
              }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </Link>
      </div>
    </article>
  );
};

export default ProjectCard;
