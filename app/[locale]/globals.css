html {
  scroll-behavior: smooth;
}

@font-face {
  font-family: "Outfit";
  src: local("Outfit"),
    url("../Fonts/Outfit-VariableFont_wght.ttf") format("truetype");
  font-display: swap;
  size-adjust: 100%;
  ascent-override: 90%;
  descent-override: 20%;
  line-gap-override: 0%;
}

@font-face {
  font-family: "Montserrat";
  src: local("Montserrat"),
    url("../Fonts/Montserrat-VariableFont_wght.ttf") format("truetype");
  font-display: swap;
  size-adjust: 100%;
  ascent-override: 90%;
  descent-override: 20%;
  line-gap-override: 0%;
}

@font-face {
  font-family: "Poppins";
  src: local("Poppins"), url("../Fonts/Poppins-Regular.ttf") format("truetype");
  font-display: swap;
  size-adjust: 100%;
  ascent-override: 90%;
  descent-override: 20%;
  line-gap-override: 0%;
}

@font-face {
  font-family: "Zain";
  src: local("Zain"), url("../Fonts/Zain-Regular.ttf") format("truetype");
  font-display: swap;
  size-adjust: 100%;
  ascent-override: 90%;
  descent-override: 20%;
  line-gap-override: 0%;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles to prevent layout shifts */
:root {
  --font-fallback: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol";
}

@layer utilities {
  .invoice {
    @apply flex flex-col h-fit gap-3 md:w-full w-[85%] z-10 mx-auto mb-5 md:mb-0 rounded-xl;
  }

  .page-content {
    @apply md:w-full p-10 rounded-xl hidden bg-white shadow-lg shadow-blue-100 md:flex justify-between items-start md:gap-5 flex-col md:flex-row md:sticky md:mt-[20rem] mt-[20px];
  }
}

.StartBtnBG {
  background: transparent;
}

.StartBtnBG:hover {
  background: linear-gradient(to top right, #003449, #6aad38);
  filter: saturate(1);
}

.serviceIconBG {
  background: linear-gradient(to bottom right, #45d692, #7ac64d);
}

.zain-regular {
  font-family: "Zain", var(--font-fallback);
}

.poppins-regular {
  font-family: "Poppins", var(--font-fallback);
}

.outfit {
  font-family: "Outfit", var(--font-fallback);
}

.montserrat {
  font-family: "Montserrat", var(--font-fallback);
}

.CustomRadialGradiant1 {
  background: radial-gradient(#005171, #002736);
}

.TextGradiant {
  background: -webkit-linear-gradient(#ffffffc9, #ffffffc9, #919191);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media screen and (min-width: 1280px) {
  .ServiceBG {
    background: linear-gradient(to top, #71b331, #cfcfcf);
  }
}

.CreativeGradiant {
  background: -webkit-linear-gradient(left, #005171, #71b331);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 12px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
  border: 3px solid transparent;
  background-clip: content-box;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/*HERE YOU CAN ADD THE SCROLL BAR TO THE GRID OR REMOVE IT*/

.scrollable-grid {
  scrollbar-width: none;
  scrollbar-color: #888 transparent;
}

.scrollable-grid::-webkit-scrollbar {
  width: 12px;
}

.scrollable-grid::-webkit-scrollbar-track {
  background: transparent;
}

.scrollable-grid::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 10px;
  border: 3px solid transparent;
  background-clip: content-box;
}

.scrollable-grid::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.mask-image {
  mask-image: linear-gradient(to top,
      rgba(255, 255, 255, 0.7),
      rgba(255, 255, 255, 0.3));
  -webkit-mask-image: linear-gradient(to top,
      rgba(255, 255, 255, 0.7),
      rgba(255, 255, 255, 0.3));
}

@layer utilities {
  .mask-fade {
    -webkit-mask-image: linear-gradient(to bottom,
        transparent,
        black 30%,
        black 75%,
        transparent);
    mask-image: linear-gradient(to bottom,
        transparent,
        black 30%,
        black 75%,
        transparent);
  }

  .mask-fade-up {
    mask-image: linear-gradient(to top,
        transparent,
        black 30%,
        black 65%,
        transparent);
    -webkit-mask-image: linear-gradient(to top,
        transparent,
        black 30%,
        black 65%,
        transparent);
  }

  /* Media query for mobile screens */
  @media (max-width: 768px) {
    .mask-fade-up {
      mask-image: linear-gradient(to top,
          transparent,
          black 20%,
          black 80%,
          transparent);
      -webkit-mask-image: linear-gradient(to top,
          transparent,
          black 40%,
          black 80%,
          transparent);
    }
  }
}

/* Mobile and Tablet Styles */
.service-card {
  border-radius: 1.5rem;
  /* rounded-2xl for all cards on mobile/tablet */
}

/* Desktop Styles */

@media screen and (max-width: 800px) {
  html {
    font-size: 14px;
  }
}

@media screen and (min-width: 810px) and (max-width: 1910px) {
  html {
    font-size: 14px;
  }
}

@media screen and (min-width: 1990px) {
  html {
    font-size: 26px;
  }
}

@media screen and (min-width: 3839px) {
  html {
    font-size: 35px;
  }
}

@media screen and (min-width: 7670px) {
  html {
    font-size: 75px;
  }
}

.FrontBG {
  filter: blur(0.3rem) brightness(0.5);
}

/* Custom Scrollbar for Desktops */
@media (min-width: 1280) {
  ::-webkit-scrollbar {
    width: 12px;
    /* Width of the entire scrollbar */
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    /* Background of the scrollbar track */
    border-radius: 10px;
    /* Rounding the corners of the scrollbar track */
  }

  ::-webkit-scrollbar-thumb {
    background-color: #b0c4de;
    /* Color of the scrollbar thumb */
    border-radius: 10px;
    /* Rounding the corners of the scrollbar thumb */
    border: 3px solid #f1f1f1;
    /* Creates padding around thumb */
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #a3b8d3;
    /* Darker color on hover for the scrollbar thumb */
  }

  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    /* Make the scrollbar thin */
    scrollbar-color: #b0c4de #f1f1f1;
    /* Thumb color and track color */
  }

  /* Edge and IE scrollbar */
  body {
    -ms-overflow-style: none;
    /* Hides default scrollbar for IE */
  }

  /* For old IE versions (optional) */
  html {
    scrollbar-face-color: #b0c4de;
    /* Color of the scrollbar thumb */
    scrollbar-track-color: #f1f1f1;
    /* Color of the scrollbar track */
  }
}

/* Hide Scrollbar for Mobile and Tablets */
@media (max-width: 1280) {
  ::-webkit-scrollbar {
    display: none;
    /* Hide the scrollbar */
  }

  /* Firefox */
  * {
    scrollbar-width: none;
    /* Hide scrollbar */
  }

  /* Edge and IE */
  body {
    -ms-overflow-style: none;
    /* Hide scrollbar */
  }
}

@layer base {
  :root {
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  /* body {
    @apply bg-background text-foreground;
  } */
}