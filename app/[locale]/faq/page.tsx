import Image from "next/image";
import React from "react";
import { Metadata } from "next";
import arabic_letter from "../../../public/bgs/LargeBG.svg";
import initTranslations from "@/app/i18n";
import FAQ from "../Components/FAQ";
import NavBar from "../Components/NavBar";
import TranslationsProvider from "../Components/TranslationsProvider";
import Footer from "../Components/Footer";

interface PageProps {
  params: {
    locale: string;
  };
}

export async function generateMetadata({
  params: { locale },
}: PageProps): Promise<Metadata> {
  const { t } = await initTranslations(locale, ["default"]);

  // Set canonical URL based on locale
  const canonicalUrl = locale === 'ar'
    ? "https://www.go-tru.com/ar/faq"
    : "https://www.go-tru.com/faq";

  const title = locale === 'ar' ? 'الأسئلة الشائعة | GoTru' : 'FAQ - Frequently Asked Questions | GoTru';

  return {
    title,
    description: t("seoText.faqDescription"),
    openGraph: {
      title: t("seoText.faqOgTitle"),
      description: t("seoText.faqOgDescription"),
      url: canonicalUrl,
      siteName: "GoTru",
      images: [
        {
          url: "/icon.webp",
          width: 1200,
          height: 630,
          alt: "GoTru Logo",
        },
      ],
      locale: locale,
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: t("seoText.faqTwitterTitle"),
      description: t("seoText.faqTwitterDescription"),
      images: ["/icon.webp"],
    },
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'en': 'https://www.go-tru.com/faq',
        'ar': 'https://www.go-tru.com/ar/faq',
      },
    },
  };
}

const page = async ({ params: { locale } }: PageProps) => {
  const i18nNamespaces = ["default"];
  const currentLocale = locale;
  const { t, resources } = await initTranslations(
    currentLocale,
    i18nNamespaces
  );
  return (
    <main className=" overflow-x-hidden">
      <TranslationsProvider
        namespaces={i18nNamespaces}
        locale={locale}
        resources={resources}
      >
        <div>
          <NavBar locale={currentLocale} />
          <Image
            src={arabic_letter}
            alt="Arabic Calligraphy Background" // Descriptive alt text
            className="fixed z-[-10] w-full bg-none h-full object-cover object-center opacity-70 "
            priority
          />
          <FAQ locale={locale} t={t} />
        </div>
        <Footer t={t} locale={locale} />
      </TranslationsProvider>
    </main>
  );
};

export default page;
