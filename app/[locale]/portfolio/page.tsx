import React from "react";
import { Metadata } from "next";
import TranslationsProvider from "../Components/TranslationsProvider";
import initTranslations from "../../i18n";
import NavBar from "../Components/NavBar";
import Footer from "../Components/Footer";
import PortfolioGrid from "../Components/Portfolio-Section/PortfolioGrid";
import Image from "next/image";
import arabic_letter from "../../../public/bgs/LargeBG.svg";

interface PortfolioPageProps {
  params: {
    locale: string;
  };
}

const i18nNamespaces = ["default"];

export async function generateMetadata({
  params: { locale },
}: PortfolioPageProps): Promise<Metadata> {
  const { t } = await initTranslations(locale, ["default"]);

  // Set canonical URL based on locale
  const canonicalUrl = locale === 'ar'
    ? "https://www.go-tru.com/ar/portfolio"
    : "https://www.go-tru.com/portfolio";

  const title = locale === 'ar' ? 'أعمالنا - GoTru' : 'Portfolio - GoTru';

  return {
    title,
    description: t("portfolioText.sectionDescription"),
    openGraph: {
      title,
      description: t("portfolioText.sectionDescription"),
      url: canonicalUrl,
      siteName: "GoTru",
      images: [
        {
          url: "/icon.webp",
          width: 1200,
          height: 630,
          alt: "GoTru Portfolio",
        },
      ],
      locale: locale,
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description: t("portfolioText.sectionDescription"),
      images: ["/icon.webp"],
    },
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'en': 'https://www.go-tru.com/portfolio',
        'ar': 'https://www.go-tru.com/ar/portfolio',
      },
    },
  };
}

export default async function PortfolioPage({ params: { locale } }: PortfolioPageProps) {
  const currentLocale = locale;
  const isArabic = locale === "ar";

  const { t, resources } = await initTranslations(
    currentLocale,
    i18nNamespaces
  );

  return (
    <main className="w-full h-full">
      <TranslationsProvider
        namespaces={i18nNamespaces}
        locale={locale}
        resources={resources}
      >
        <div className="relative overflow-hidden min-h-screen">
          <Image
            src={arabic_letter}
            alt="Arabic Calligraphy Background"
            className="absolute z-[-10] w-full h-full object-cover object-center opacity-60"
            priority
          />
          
          <header className="flex justify-center items-center">
            <NavBar locale={locale} />
          </header>

          {/* Page Header */}
          <section className="w-full relative flex items-center justify-center flex-col pt-[8rem] md:pt-[11rem] pb-8">
            <div className="w-full px-4 md:px-8 xl:w-4/5 flex flex-col items-center justify-center">
              <h1
                className={`uppercase text-[#005171] pb-4 text-center ${
                  isArabic
                    ? "zain-regular font-semibold text-[3.2rem] md:text-[4.8rem] xl:text-[5.4rem]"
                    : "outfit font-medium text-[3.2rem] md:text-[4.8rem] xl:text-[5.4rem]"
                }`}
              >
                {t("portfolioText.sectionTitle")}
              </h1>
              
              <p
                className={`text-[#58626c] font-normal w-full md:w-4/5 xl:w-2/3 leading-relaxed md:leading-loose xl:leading-[3rem] ${
                  isArabic
                    ? "zain-regular text-[1.6rem] xl:text-[2.4rem]"
                    : "outfit text-[1.6rem] xl:text-[2.4rem]"
                } text-center mb-4`}
                dir={isArabic ? "rtl" : "ltr"}
              >
                {t("portfolioText.sectionSubtitle")}
              </p>

              <p
                className={`text-[#58626c] opacity-80 font-normal w-full md:w-4/5 xl:w-3/4 leading-relaxed ${
                  isArabic
                    ? "zain-regular text-[1.2rem] xl:text-[1.6rem]"
                    : "outfit text-[1.2rem] xl:text-[1.6rem]"
                } text-center`}
                dir={isArabic ? "rtl" : "ltr"}
              >
                {t("portfolioText.sectionDescription")}
              </p>
            </div>
          </section>

          {/* Portfolio Grid */}
          <section className="w-full relative flex items-center justify-center flex-col pb-16">
            <PortfolioGrid t={t} locale={locale} />
          </section>

          <footer>
            <Footer t={t} locale={locale} />
          </footer>
        </div>
      </TranslationsProvider>
    </main>
  );
}
