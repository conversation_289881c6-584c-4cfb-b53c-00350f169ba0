import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import <PERSON>ript from "next/script";
import { headers } from "next/headers";
import initTranslations from "../i18n";
import QueryProvider from "./Components/QueryClientProvider";
import { Toaster } from "react-hot-toast";
import ClarityProvider from "./Components/ClarityProvider";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  preload: true,
  variable: "--font-inter",
});

export const metadata: Metadata = {
  metadataBase: new URL("https://www.go-tru.com"),
  title: {
    template: "%s | Go Tru",
    default: "Go Tru - Software Development & Tech Consulting",
  },
  description:
    "Go Tru (GoTru) specializes in innovative software development, mobile and web app creation, tech consulting",
  alternates: {
    canonical: "https://www.go-tru.com",
    languages: {
      en: "https://www.go-tru.com/",
      ar: "https://www.go-tru.com/ar",
    },
  },
  openGraph: {
    title: "Go Tru - Software Development & Tech Consulting",
    description:
      "Go Tru (GoTru) specializes in innovative software development, mobile and web app creation, tech consulting",
    url: "https://www.go-tru.com",
    siteName: "GoTru",
    images: [
      {
        url: "/icon.webp",
        width: 1200,
        height: 630,
        alt: "GoTru Logo",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Go Tru - Software Development & Tech Consulting",
    description:
      "Leading software development and tech consulting agency specializing in web development, mobile apps, and cloud services.",
    images: ["/icon.webp"],
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 5,
  },
  icons: {
    icon: "/favicon.ico",
    apple: "/favicon.ico",
  },
};

interface Params {
  locale: string;
}

export default async function RootLayout({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: Params;
}) {
  const i18nNamespaces = ["default"];
  const currentLocale = locale;
  const { t } = await initTranslations(currentLocale, i18nNamespaces);

  const schemaOrgJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "Go Tru",
    url: "https://www.go-tru.com",
    description: t("seoText.description"),
    alternateName: ["GoTru", "gotru", "go-tru", "gotrue"],
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "+971528281927",
      contactType: "Customer Service",
      availableLanguage: ["en", "ar"],
    },
    sameAs: [
      "https://www.instagram.com/gotru.ae/",
      "https://wa.me/+971528281927",
    ],
  };

  const headersList = headers();
  const pathname = headersList.get("x-pathname") || "/";
  const canonicalUrl = locale == 'ar' ? `https://www.go-tru.com/${locale}${pathname}` : `https://www.go-tru.com/${pathname}`;

  return (
    <html lang={locale} className={inter.variable}>
      <head>
        {/* Preload Fonts */}
        <link
          rel="preload"
          href="/fonts/Zain-Regular.ttf"
          as="font"
          type="font/ttf"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/Montserrat.ttf"
          as="font"
          type="font/ttf"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/Poppins-Regular.ttf"
          as="font"
          type="font/ttf"
          crossOrigin="anonymous"
        />

        {/* Primary Meta Tags */}
        <meta charSet="UTF-8" />
        <meta name="author" content="Go Tru" />
        <meta name="robots" content="index, follow" />
        <meta name="theme-color" content="#005171" />

        {/* Keywords Meta Tag for SEO */}
        <meta name="keywords" content={t("seoText.keywords")} />

        {/* Additional name variations for SEO */}
        <meta name="name-variations" content="gotru, go tru, go-tru, gotrue" />

        {/* Favicon and App Icons */}
        <link rel="icon" type="image/x-icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/favicon.ico" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black" />

        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaOrgJsonLd) }}
        />

        {/* Canonical and Alternate Links */}
        <link rel="canonical" href={canonicalUrl} />
        <link
          rel="alternate"
          hrefLang={locale}
          href={locale == 'ar' ? `https://www.go-tru.com/${locale}` : "https://www.go-tru.com"}
        />
        {/* <link
          rel="alternate"
          hrefLang={locale === "en" ? "ar" : "en"}
          href={`https://www.go-tru.com/${locale === "en" ? "ar" : "en"}`}
        /> */}

        {/* Google Analytics Script */}
        <Script
          async
          src="https://www.googletagmanager.com/gtag/js?id=G-X4LWQV8KP0"
        ></Script>
        <Script id="google-analytics">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-X4LWQV8KP0');
          `}
        </Script>
      </head>

      <body suppressHydrationWarning={true} className={`${inter.className} `}>
        <ClarityProvider>
          <QueryProvider>
            {children}
            <Toaster />
          </QueryProvider>
        </ClarityProvider>
      </body>
    </html>
  );
}
