import { MetadataRoute } from "next";

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = "https://www.go-tru.com";

  // Define available pricing domains (only the ones that actually exist)
  const pricingDomains = [
    'e-commerce',
    'medical'
  ];

  // Generate pricing pages for each domain and locale
  const pricingPages = pricingDomains.flatMap(domain => [
    // Overview pages - English (canonical)
    {
      url: `${baseUrl}/pricing/${domain}/overview`,
      lastModified: new Date(),
      changeFrequency: "monthly" as const,
      priority: 0.7,
    },
    // Overview pages - Arabic
    {
      url: `${baseUrl}/ar/pricing/${domain}/overview`,
      lastModified: new Date(),
      changeFrequency: "monthly" as const,
      priority: 0.7,
    },
    // Tool pages - English (canonical)
    {
      url: `${baseUrl}/pricing/${domain}/tool`,
      lastModified: new Date(),
      changeFrequency: "monthly" as const,
      priority: 0.6,
    },
    // Tool pages - Arabic
    {
      url: `${baseUrl}/ar/pricing/${domain}/tool`,
      lastModified: new Date(),
      changeFrequency: "monthly" as const,
      priority: 0.6,
    },
  ]);

  return [
    {
      url: "https://www.go-tru.com",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 1,
    },
    {
      url: "https://www.go-tru.com/ar",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 1,
    },
    {
      url: "https://www.go-tru.com/portfolio",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    {
      url: "https://www.go-tru.com/ar/portfolio",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    {
      url: "https://www.go-tru.com/faq",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.7,
    },
    {
      url: "https://www.go-tru.com/ar/faq",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.7,
    },
    {
      url: "https://www.go-tru.com/blog",
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.8,
    },
    {
      url: "https://www.go-tru.com/ar/blog",
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.8,
    },
    // Add all pricing domain pages
    ...pricingPages,
  ];
}
