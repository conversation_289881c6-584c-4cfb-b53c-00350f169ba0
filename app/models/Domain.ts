import mongoose from 'mongoose';

const DomainSchema = new mongoose.Schema({
  title_en: { type: String, required: true },
  title_ar: { type: String, required: true },
  description_en: { type: String, required: true },
  description_ar: { type: String, required: true },
  preview_title_en: { type: String, required: true },
  preview_title_ar: { type: String, required: true },
  preview_description_en: { type: String, required: true },
  preview_description_ar: { type: String, required: true },
  icon: { type: String, required: true },
  screenshots: [{ type: String }], // Array of screenshot URLs/paths
  starting_price: { type: Number, required: true },
  slug: { type: String, required: true },
  disabled: { type: Boolean, required: true }
});

export default mongoose.models.Domain || mongoose.model('Domain', DomainSchema);