import mongoose from 'mongoose';

const Request_FeatureSchema = new mongoose.Schema({
  request_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Request',
    required: true
  },
  feature_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Feature',
    required: true
  },
  final_price: { type: Number, required: true }
});

export default mongoose.models.Request_Feature || mongoose.model('Request_Feature', Request_FeatureSchema);