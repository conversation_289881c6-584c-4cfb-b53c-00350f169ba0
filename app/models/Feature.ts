import mongoose from "mongoose";

export interface Feature {
  title_en: string;
  title_ar: string;
  description_en: string;
  description_ar: string;
  type: "Basic" | "Optional" | "Add_On";
  backend_time: number;
  web_time: number;
  mobile_time: number;
  devops_time: number;
  QA_time: number;
  base_price: number;
  domain_id: string;
}

const FeatureSchema = new mongoose.Schema({
  title_en: { type: String, required: true },
  title_ar: { type: String, required: true },
  description_en: { type: String, required: true },
  description_ar: { type: String, required: true },
  type: {
    type: String,
    required: true,
    enum: ["Basic", "Optional", "Add_On"],
  },
  category: { type: String, required: true },
  role_category: { type: String, required: true },
  base_price: { type: Number, required: true },
  web_time: { type: Number, required: true },
  mobile_time: { type: Number, required: true },
  backend_time: { type: Number, required: true },
  design_time: { type: Number, required: true },
  devops_time: { type: Number, required: true },
  QA_time: { type: Number, required: true },
  domain_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Domain",

  },
});

export default mongoose.models.Feature ||
  mongoose.model("Feature", FeatureSchema);
