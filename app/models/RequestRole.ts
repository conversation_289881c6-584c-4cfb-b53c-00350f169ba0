import mongoose from "mongoose";

const Request_RoleSchema = new mongoose.Schema({
  request_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Request",
    required: true,
  },

  role_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Role",
    required: true,
  },
  required_members: { type: Number, required: true },
});

export default mongoose.models.Request_Role ||
  mongoose.model("Request_Role", Request_RoleSchema);
