import mongoose from 'mongoose';
import { string } from 'zod';

const RoleSchema = new mongoose.Schema({
  title_en: { type: String, required: true },
  title_ar: { type: String, required: true },
  description_en: { type: String, required: true },
  description_ar: { type: String, required: true },
  cost: { type: Number, required: true },
  category: { type: String, required: true }
});

export default mongoose.models.Role || mongoose.model('Role', RoleSchema);