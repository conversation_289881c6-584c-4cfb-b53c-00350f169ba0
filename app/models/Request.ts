import mongoose from "mongoose";

const RequestSchema = new mongoose.Schema({
  client_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Client",
    required: true,
  },
  total_time: { type: Number, required: true },
  total_price: { type: Number, required: true },
  platforms: { type: Array<string>, required: true },
});

export default mongoose.models.Request ||
  mongoose.model("Request", RequestSchema);
