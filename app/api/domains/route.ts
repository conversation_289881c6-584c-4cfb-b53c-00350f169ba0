import Domain from "@/app/models/Domain";
import { ApiError } from "@/lib/ApiError";
import { connectDB } from "@/lib/db";
import { handleApiError } from "@/lib/errorHandler";
import { NextResponse } from "next/server";

export const POST = async (req: Request) => {
  try {
    await connectDB();
    const {
      title_en,
      title_ar,
      description_en,
      description_ar,
      icon,
      disabled,
      starting_price,
    } = await req.json();

    if (
      !title_en ||
      !title_ar ||
      !description_en ||
      !description_ar ||
      !icon ||
      !starting_price ||
      starting_price <= 0
    ) {
      throw ApiError.badRequest();
    }

    const newDomain = await Domain.create({
      title_en,
      title_ar,
      description_en,
      description_ar,
      icon,
      disabled,
      starting_price,
    });

    return NextResponse.json(
      {
        success: true,
        data: newDomain,
      },
      { status: 201 }
    );
  } catch (error) {
    return handleApiError(error);
  }
};

export const GET = async (req: Request) => {
  try {
    await connectDB();

    const locale = req.headers.get("locale") || "en";

    const domains = await Domain.find();
    if (!domains || domains.length <= 0) {
      throw ApiError.notFound("No data found");
    }
    const localizedDomains = domains.map((domain) => ({
      id: domain.id,
      title: locale === "ar" ? domain.title_ar : domain.title_en,
      description:
        locale === "ar" ? domain.description_ar : domain.description_en,
      preview_title: locale === "ar" ? domain.preview_title_ar : domain.preview_title_en,
      preview_description:
        locale === "ar" ? domain.preview_description_ar : domain.preview_description_en,
      starting_price: domain.starting_price,
      icon: domain.icon,
      screenshots: domain.screenshots || [],
      slug: domain.slug,
      disabled: domain.disabled
    }));

    return NextResponse.json(
      {
        success: true,
        data: {
          domains: localizedDomains,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    return handleApiError(error);
  }
};
