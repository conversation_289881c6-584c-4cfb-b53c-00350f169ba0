import Feature from "@/app/models/Feature";

import { connectDB } from "@/lib/db";
import { NextResponse } from "next/server";
import { ApiError } from "@/lib/ApiError";
import { handleApiError } from "@/lib/errorHandler";
import FeatureRole from "@/app/models/FeatureRole";
import Role from "@/app/models/Role";
import Domain from "@/app/models/Domain";
enum Types {
  "Basic",
  "Optional",
  "Add_On",
}

function checkFeatureType(type: string): boolean {
  return type in Types;
}

export const POST = async (req: Request) => {
  try {
    await connectDB();
    const {
      title_en,
      title_ar,
      description_en,
      description_ar,
      type,
      category,
      base_price,
      backend_time,
      design_time,
      web_time,
      QA_time,
      devops_time,
      mobile_time,
      domain_id,
      roles,
    } = await req.json();

    if (
      !title_en ||
      !title_ar ||
      !description_en ||
      !description_ar ||
      !type ||
      !category ||
      !base_price ||
      !domain_id ||
      !roles ||
      roles.length <= 0
    ) {
      throw ApiError.badRequest("Missing credentials");
    }

    if (!checkFeatureType(type)) {
      throw ApiError.badRequest(
        "Feature type must be Basic, Optional, or Add_On"
      );
    }

    const domainExist = await Domain.findById(domain_id);

    if (!domainExist) {
      throw ApiError.notFound("invalid domain");
    }

    const newFeature = await Feature.create({
      title_en,
      title_ar,
      description_en,
      description_ar,
      type,
      category,
      base_price,
      backend_time,
      design_time,
      web_time,
      QA_time,
      devops_time,
      mobile_time,
      domain_id,
    });

    const createNewRoleFeature = async (roleId: any) => {
      const roleExist = await Role.findOne(roleId);

      if (roleExist) {
        const newFeatureRole = await FeatureRole.create({
          feature_id: newFeature.id,
          role_id: roleId,
        });
      }
    };

    const featureRoles = roles.map((role: any) => {
      createNewRoleFeature(role);
    });

    return NextResponse.json(
      {
        success: true,
        message: "New Feature Added",
        feature: newFeature,
      },
      { status: 201 }
    );
  } catch (error) {
    return handleApiError(error);
  }
};

export const GET = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const slug = url.searchParams.get("slug");
    const locale = req.headers.get("locale");

    if (!slug || !locale) {
      throw ApiError.badRequest("Missing domainId or locale");
    }
    const domainExist = await Domain.findOne({ slug: slug });
    if (!domainExist) {
      throw ApiError.notFound("Invalid Domain")
    }
    const features = await Feature.find({ domain_id: domainExist.id });

    if (features.length <= 1) {
      throw ApiError.notFound("No features found");
    }

    const localizedFeatures = features.map((feature) => ({
      id: feature.id,
      title: feature[`title_${locale}`],
      description: feature[`description_${locale}`],
      type: feature.type,
      category: feature.category,
      role_category: feature.role_category,
      base_price: feature.base_price,
      backend_time: feature.backend_time,
      design_time: feature.design_time,
      web_time: feature.web_time,
      QA_time: feature.QA_time,
      devops_time: feature.devops_time,
      mobile_time: feature.mobile_time,
    }));

    return NextResponse.json(
      {
        success: true,
        data: {
          features: localizedFeatures,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    return handleApiError(error);
  }
};
