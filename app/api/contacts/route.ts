import { NextResponse } from "next/server";
import { connectDB } from "@/lib/db";
import Contact from "../../models/Contact";
import Feature from "@/app/models/Feature";
import mongoose from "mongoose";
const nodemailer = require("nodemailer");

export async function POST(req: Request) {
  try {
    await connectDB();
    // await Feature.insertMany(features);
    // console.log("Feature seeding completed!");
    const { email, phone, message } = await req.json();

    if (!email || !phone || !message) {
      return NextResponse.json(
        { message: "All fields are required." },
        { status: 400 }
      );
    }

    const newMessage = await Contact.create({ email, phone, message });
    const transporter = nodemailer.createTransport({
      host: "mail.go-tru.com", // Outgoing SMTP server
      port: 465, // SMTP port for SSL
      secure: true, // Use SSL/TLS
      auth: {
        user: "<EMAIL>", // Your email address
        pass: process.env.EMAIL_PASSWORD, // Your email account's password
      },
    });

    const mailOptions = {
      from: "<EMAIL>", // Sender address
      to: email, // Recipient address
      subject: message, // Email subject
      text: `we recived your request, we'll contact you soon \n\n Request Details:\n\nEmail: ${email}\nPhone: ${phone}\nMessage: ${message}`, // Email body
    };

    await transporter.sendMail(mailOptions);

    return NextResponse.json(
      { message: "Message saved successfully!", contact: newMessage },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error saving contact message:", error);
    return NextResponse.json(
      { message: "Internal Server Error" },
      { status: 500 }
    );
  }
}
