import { NextResponse } from "next/server";
import { connectDB } from "@/lib/db";
import Role from "@/app/models/Role";
import { ApiError } from "@/lib/ApiError";
import { handleApiError } from "@/lib/errorHandler";

export const POST = async (req: Request) => {
  try {
    await connectDB();

    const { checkedRoles } = await req.json();

    if (!Array.isArray(checkedRoles) || checkedRoles.length === 0) {
      throw ApiError.badRequest("Invalid or empty checkedRoles array");
    }

    const roleIds = checkedRoles.map((role) => role.id);

    const roles = await Role.find({ _id: { $in: roleIds } });

    if (!roles || roles.length === 0) {
      throw ApiError.notFound("No roles found");
    }
    let cost = 0;
    checkedRoles.forEach((checkedRole) => {
      const role = roles.find((r) => r.id === checkedRole.id);
      if (role) {
        cost += role.cost * checkedRole.number;
      }
    });

    return NextResponse.json(
      {
        success: true,
        cost: cost,
      },
      { status: 200 }
    );
  } catch (error) {
    return handleApiError(error);
  }
};
