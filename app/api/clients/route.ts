import Client from "@/app/models/Client";
import Request from "@/app/models/Request";
import Request_Feature from "../../models/RequestFeature";
import Request_Role from "../../models/RequestRole";
import { NextResponse } from "next/server";
import { connectDB } from "@/lib/db";
import { ApiError } from "@/lib/ApiError";
import { handleApiError } from "@/lib/errorHandler";


interface BasicDocument {
  _id: any;
  [key: string]: any;
} 
export const POST = async (req: Request) => {
  await connectDB();

  try {
    const {
      name,
      email,
      phone,
      total_time,
      total_price,
      checkedFeatures,
      checkedRoles,
      platform,
    } = await req.json();

    // Validate required fields
    if (
      !name ||
      !email ||
      !phone ||
      !total_time ||
      !total_price ||
      !Array.isArray(checkedFeatures) ||
      !Array.isArray(checkedRoles) ||
      !Array.isArray(platform)
    ) {
      throw new ApiError(400, "Missing required fields or invalid data format");
    }

    // Find or create client
    let client = await Client.findOne({ email });
    if (!client) {
      client = await Client.create({ name, email, phone });
    }

    // Create request
    const newRequest = await Request.create({
      client_id: client._id,
      total_time,
      total_price,
      platforms: platform,
    });

    // Create and store request features
    const createdFeatures = await Promise.all(
      checkedFeatures.map((f) =>
        Request_Feature.create({
          request_id: newRequest._id,
          feature_id: f.id,
          final_price: f.base_price,
        })
      )
    );

    // Create and store request roles
    const createdRoles = await Promise.all(
      checkedRoles.map((r) =>
        Request_Role.create({
          request_id: newRequest._id,
          role_id: r.id,
          required_members: r.number,
        })
      )
    );

    return NextResponse.json(
      {
        message: "Request created successfully",
        client,
        request: newRequest,
        features: createdFeatures,
        roles: createdRoles,
      },
      { status: 201 }
    );
  } catch (error) {
    return handleApiError(error);
  }
};

export const GET = async (req: Request) => {
  try {
    await connectDB();

    // Get email from query parameters
    const { searchParams } = new URL(req.url);
    const email = searchParams.get("email");

    if (!email) {
      throw new ApiError(400, "Email parameter is required");
    }

    // Find client by email and populate all related data
  const client = await Client.findOne({ email }).lean().exec() as BasicDocument;


    if (!client) {
      throw ApiError.notFound("Client not found");
    }

    // Get all requests for this client
    const requests = await Request.find({ client_id: client._id })
      .lean()
      .exec();

    // Get all request IDs for relationship queries
    const requestIds = requests.map((r) => r._id);

    // Get related features and roles in parallel
    const [features, roles] = await Promise.all([
      Request_Feature.find({ request_id: { $in: requestIds } }).lean(),
      Request_Role.find({ request_id: { $in: requestIds } }).lean(),
    ]);

    // Map requests with their relationships
    const requestsWithDetails = requests.map((request: any) => ({
      ...request,
      features: features.filter(
        (f) => f.request_id.toString() === request._id.toString()
      ),
      roles: roles.filter(
        (r) => r.request_id.toString() === request._id.toString()
      ),
    }));

    return NextResponse.json({
      success: true,
      data: {
        client: {
          ...client,
          requests: requestsWithDetails,
        },
      },
      status_code: 200,
    });
  } catch (error) {
    return handleApiError(error);
  }
};
