import Client from "@/app/models/Client";
import Feature from "@/app/models/Feature";
import Request from "@/app/models/Request";
import Role from "@/app/models/Role";
import { ApiError } from "@/lib/ApiError";
import { connectDB } from "@/lib/db";
import { handleApiError } from "@/lib/errorHandler";
import mongoose from "mongoose";
import { NextResponse } from "next/server";

interface RecivedRole {
  role_id: string;
  required_number: number;
}

const calculatePrice = async (features: string[], roles: RecivedRole[]) => {
    let featuresPrice = 0;
  
    // Properly wait for feature price calculations
    const featurePrices = await Promise.all(features.map(async (featureId) => {
      const feature = await Feature.findById(featureId);
      return feature ? feature.base_price : 0;
    }));
  
    featuresPrice += featurePrices.reduce((sum, price) => sum + price, 0);
  
    // Properly wait for role calculations
    const roleExtras = await Promise.all(roles.map(async (role) => {
      const roleExist = await Role.findById(role.role_id);
      return roleExist && role.required_number > 1 ? 500 : 0;
    }));
  
    featuresPrice += roleExtras.reduce((sum:any, extra) => sum + extra, 0);
  
    return { total_price: featuresPrice };
  };
export const POST = async (req: Request) => {
  try {
    await connectDB();
    const { features, roles, client } = await req.json();

    if (!features || features.length <= 0 || !roles || roles.length <= 0) {
      throw ApiError.badRequest("invalid credintials");
    }

    if (!client || !client.name || !client.email || !client.phone) {
      throw ApiError.badRequest("client info are required");
    }

    const clientExist = await Client.findOne({ email: client.email });

    if (clientExist) {
        
      const { total_price } = await calculatePrice(features, roles);
      const newRequest = await Request.create({
        client_id: clientExist.id,
        total_price,
      });

      return NextResponse.json(
        {
          success: true,
          data: { newRequest, contact: clientExist },
        },
        { status: 201 }
      );
    } else if (!clientExist) {
        
      const newClient = await Client.create(client);
      const { total_price } = await calculatePrice(features, roles);
      const newRequest = await Request.create({
        client_id: newClient.id,
        total_price,
      });
      
      return NextResponse.json(
        {
          success: true,
          data: { newRequest, contact: newClient },
        },
        { status: 201 }
      );
    }

  } catch (error) {
    return handleApiError(error);
  }
};
