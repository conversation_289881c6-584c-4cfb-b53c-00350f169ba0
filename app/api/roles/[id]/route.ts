import Role from "@/app/models/Role";
import { ApiError } from "@/lib/ApiError";
import { connectDB } from "@/lib/db";
import { handleApiError } from "@/lib/errorHandler";
import { NextResponse } from "next/server";

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    const role = await Role.findById(params.id);
    if (!role) {
      throw ApiError.notFound("role not found");
    }
    return NextResponse.json(role, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}
