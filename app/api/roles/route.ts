import { NextResponse } from "next/server";
import { connectDB } from "@/lib/db";
import Role from "@/app/models/Role";
import { ApiError } from "@/lib/ApiError";
import { handleApiError } from "@/lib/errorHandler";
export const POST = async (req: Request) => {
  try {
    connectDB();
    const { title_en, title_ar, description_ar, description_en, cost, category } =
      await req.json();

    if (!title_en || !title_ar || !description_ar || !description_en || !cost) {
      throw ApiError.badRequest("missing credintials");
    }

    const newRole = await Role.create({
      title_en,
      title_ar,
      description_ar,
      description_en,
      cost,
      category,
    });

    return NextResponse.json(
      {
        success: true,
        data: {
          newRole,
          message: "new role created successfully",
        },
      },
      { status: 201 }
    );
  } catch (error) {
    return handleApiError(error);
  }
};

export const GET = async (req: Request) => {
  try {
    const locale = req.headers.get("locale");
    if (!locale) {
      throw ApiError.badRequest("Locale header is missing");
    }
    await connectDB();
    const roles = await Role.find();
    if (roles.length <= 0) {
      throw ApiError.notFound("No roles found");
    }
    const localizedRoles = roles.map((role) => {
      const localizedRole = {
        id: role["id"],
        title: role[`title_${locale}`] || role.title_en, // Default to 'title_en' if locale is not available
        description: role[`description_${locale}`] || role.description_en, // Default to 'description_en' if locale is not available
        cost: role["cost"],
        category: role["category"]
      };

      return localizedRole;
    });
    return NextResponse.json(
      {
        success: true,
        data: {
          roles: localizedRoles,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    return handleApiError(error);
  }
};



