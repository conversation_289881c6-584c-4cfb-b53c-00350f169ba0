{"about": "About", "services": "Services", "technologies": "Technologies", "portfolio": "Portfolio", "work": "Work", "contact": "Contact", "go_tru": "GoTru", "pricing": "Pricing", "heroHeadline": "Expert Teams, Perfect Fit", "outcome": "Outcome", "delivered": "Delivered", "heroDescription": {"textBeforeBold1": "Our software specialized teams guarantee", "bold1": "high-quality", "textBetweenBold1Bold2": "technical solutions at", "bold2": "optimized costs", "textBetweenBold2Bold3": ", providing the", "bold3": "ideal talent", "textAfterBold3": "for your project and ensuring its successful completion on time and within budget."}, "contactUs": "Get In Touch", "aboutUs": "Why", "aboutUs2": "GoTru ?", "aboutUsDescription": "With over ten years of experience, we've learned that success goes beyond technical expertise, it's about understanding each project's unique needs. That's why we create tailored software solutions, delivering measurable, scalable results. Our passionate team uses cutting-edge technologies and agile methodologies to align with your business goals, combining creativity and technical proficiency to drive true innovation.", "missionStatement": {"title": "Mission Statement:", "description": "We see technology as a driver of real change. Our mission is to create innovative software that meets today's needs and builds a foundation for future growth."}, "notice": {"title": "Legal Notice: Gotru is a brand name operated by Al Yamama Al Thahabia Technology LLC, registered in the United Arab Emirates. All official transactions are conducted under the registered company name."}, "languageSupport": {"title": "Language Support:", "description": "Our team offers comprehensive multilingual support, ensuring seamless communication and accessibility. We aim to bridge language gaps and empower users worldwide with inclusive, innovative tools."}, "servicesText": {"services": "Our Services", "servicesHeadline": "Unlock Your Business's Full Potential with Us", "appDevelopmentTitle": "App Development", "appDevelopmentDescription": "High-performance iOS and Android apps using Flutter,Swift and Kotlin delivering a seamless and consistent user experience across devices.", "webDevelopmentTitle": "Web Development", "webDevelopmentDescription": "Modern, responsive websites built with WordPress, Angular, Vue, and React, ensuring smooth usability and enhanced engagement.", "systemDevelopmentTitle": "System Development", "systemDevelopmentDescription": "Robust and scalable systems designed to optimize your operations, leveraging the latest technologies like Node.js, PHP, and .NET.", "techConsultingTitle": "Tech Consulting", "techConsultingDescription": "Strategic analysis and planning to help you overcome technical challenges and achieve your digital goals efficiently.", "cloudServicesTitle": "Cloud Services", "cloudServicesDescription": "Secure and flexible cloud solutions powered by AWS, Google Cloud, and Azure, ensuring stability, security, and seamless scalability.", "databaseManagementTitle": "Database Management", "databaseManagementDescription": "Enhanced data performance and security using SQL and NoSQL technologies, enabling efficient handling of big data."}, "worksSection": {"Header": "Our Process", "title": "Turning your ideas into innovative digital solutions", "subheading": "Our streamlined process ensures your project is handled with care and precision at every stage", "steps": [{"title": "Initial Consultation", "description": "We start by communicating with you to understand the project details and unique requirements, ensuring we deliver the best solutions aligned with your vision and goals."}, {"title": "Planning", "description": "We create a comprehensive plan for implementing the solutions, considering budget and timelines, to ensure the project is delivered on time and within budget."}, {"title": "Desgin , Development & Testing", "description": "After approving the designs, we proceed with implementation and system development, following rigorous testing methods to ensure quality and performance."}, {"title": "Deployment & Support", "description": "Once the project passes testing and meets client approval, we deploy the solutions using the latest technologies, providing ongoing support to ensure system stability and long-term success."}]}, "technologiesText": {"sectionTitle": "Our Technologies – The Tech That Builds Your Success", "mainTitle": "We believe technology should be a solution, not an obstacle. We simplify it for you, so you can focus on growing your business while we handle the technical side.", "subtitle": "", "tagline": "", "frontend": {"title": "Front End Development", "description": "We create dynamic and modern user interfaces using the latest frontend technologies to ensure a smooth and engaging user experience."}, "backend": {"title": "Back End Development", "description": "We build strong and flexible backend systems that deliver high performance, tight security, and scalability to align with your business growth."}, "database": {"title": "Database Management", "description": "We provide reliable data management solutions that ensure high performance, security, and scalability."}, "cloud": {"title": "Cloud Services", "description": "We offer flexible and secure cloud solutions to ensure high performance and easy scalability for your technical needs."}, "mobile": {"title": "Mobile Development", "description": "We deliver innovative solutions for mobile app development using Flutter,Swift and Kotlin, ensuring a seamless and consistent user experience across all platforms."}}, "contactUsText": {"title": "Let's Work Together", "subtitle": "Let's", "brand": "GoTru", "button": "Start on WhatsApp"}, "footer": {"tagline": "Where Software Goes True", "contactTitle": "Contact Us"}, "seoText": {"description": "Go Tru - Leading software development and tech consulting agency specializing in web development, mobile apps, cloud services, and Modern solutions.", "keywords": "gotru, gotrue, go tru, go-tru, software development, app development, mobile app development, web development, tech consulting, cloud services, database management, multilingual team, AWS, Google Cloud, Azure, Node.js, PHP, .NET, Flutter, WordPress, Angular, Vue, React, SQL, NoSQL, Dubai tech company, UAE software development", "ogTitle": "Go Tru - Software Development & Tech Consulting Agency", "ogDescription": "Leading software development and tech consulting agency specializing in web development, mobile apps, cloud services, and bilingual solutions.", "twitterTitle": "Go Tru - Software Development & Tech Consulting Agency", "twitterDescription": "Leading software development and tech consulting agency specializing in web development, mobile apps, cloud services, and bilingual solutions.", "faqTitle": "FAQ - Frequently Asked Questions | GoTru", "faqDescription": "Find answers to frequently asked questions about GoTru's software development services, pricing, technologies, and project processes.", "faqOgTitle": "GoTru FAQ - Software Development Questions & Answers", "faqOgDescription": "Get answers to common questions about our software development services, technologies, pricing, and project delivery process.", "faqTwitterTitle": "GoTru FAQ - Software Development Questions & Answers", "faqTwitterDescription": "Get answers to common questions about our software development services, technologies, pricing, and project delivery process.", "pricing": {"overview_suffix": "Get detailed pricing information and project overview with GoTru's transparent pricing model.", "tool_suffix": "Use our interactive pricing calculator to estimate costs and timelines for your project."}}, "faq": {"1": {"title": "General FAQs", "1": {"q": "What is Go<PERSON><PERSON>?", "a": "GoTru is a software development agency that specializes in delivering high-quality digital solutions tailored to your unique needs. We offer services like app development, web development, system development, tech consulting, cloud services, and database management."}, "2": {"q": "What is <PERSON><PERSON><PERSON>'s mission?", "a": "Our mission is to transform your ideas into powerful digital solutions that drive success."}, "3": {"q": "Where is GoTru located?", "a": "GoTru is located in Dubai, UAE, at Ires Bay building, Business Bay."}, "4": {"q": "How can I contact GoTru?", "a": "You can reach us via phone at +971 52 828 1927, <NAME_EMAIL>, or by starting a conversation on WhatsApp."}, "5": {"q": "Does GoTru support multilingual communication?", "a": "Yes, our team includes multilingual professionals, including Arabic speakers, to assist you effectively."}}, "2": {"title": "Services FAQs", "1": {"q": "What services does GoTru offer?", "a": "GoTru offers a wide range of services, including: App Development (iOS and Android using Flutter),Web Development (WordPress, Angular, Vue, React),System Development (Node, PHP, .Net),Tech Consulting, Cloud Services (AWS, Google Cloud, Azure) Database Management (SQL and NoSQL)"}, "2": {"q": "What technologies does GoTru use for app development", "a": "We use Flutter to create innovative and seamless mobile app solutions for both iOS and Android platforms."}, "3": {"q": "Can GoTru help with web development?", "a": "Yes, we specialize in creating visually stunning and responsive websites using technologies like WordPress, Angular, Vue, and React."}, "4": {"q": "Does GoTru provide cloud services?", "a": "Yes, we offer scalable and secure cloud solutions using AWS, Google Cloud, and Azure."}, "5": {"q": "What is included in GoTru's tech consulting services?", "a": "Our tech consulting services provide expert advice to help you navigate tech challenges, optimize your digital strategy, and achieve your business goals."}}, "3": {"title": "Process FAQs", "1": {"q": "What is GoTru's approach to project delivery?", "a": "We follow a streamlined process to ensure your project is handled with care and precision at every stage, delivering results on time and within budget."}, "2": {"q": "How does GoTru ensure the right talent for my project?", "a": "Our specialized teams are carefully selected to match your project requirements, ensuring the perfect fit and high-quality outcomes."}, "3": {"q": "Does GoTru work with specific industries?", "a": "While we cater to a wide range of industries, our focus is on delivering tailored digital solutions that meet the unique needs of each client."}, "4": {"q": "", "a": ""}, "5": {"q": "", "a": ""}}, "4": {"title": "Technologies FAQs", "1": {"q": "What frontend technologies does GoTru use?", "a": "We use modern frontend technologies like WordPress, Angular, Vue, React, Next.js, and Nuxt.js to create dynamic user interfaces."}, "2": {"q": "What backend technologies does GoTru specialize in?", "a": "Our backend expertise includes Node.js, PHP, and .Net for building robust and scalable systems."}, "3": {"q": "Does GoTru handle database management?", "a": "Yes, we implement efficient database solutions using both SQL and NoSQL technologies."}, "4": {"q": "", "a": ""}, "5": {"q": "", "a": ""}}, "5": {"title": "Work and Collaboration FAQs", "1": {"q": "How can I start a project with GoTru?", "a": "You can get in touch with us via phone, email, or WhatsApp to discuss your project requirements and begin the collaboration process."}, "2": {"q": "Does GoTru work with international clients?", "a": "Yes, we work with clients globally and provide tailored solutions to meet their specific needs."}, "3": {"q": "What makes GoTru different from other agencies?", "a": "GoTru stands out for its specialized teams, optimized costs, and commitment to delivering high-quality results on time and within budget."}, "4": {"q": "Can GoTru handle large-scale projects?", "a": "Yes, we have the expertise and resources to manage large-scale projects, ensuring scalability and efficiency."}, "5": {"q": "", "a": ""}}, "6": {"title": "E-Commerce FAQs", "1": {"q": "How long will it take to launch my e-commerce website?", "a": "On average, it takes 4 weeks from the kickoff meeting to launch. This timeline includes design, development, testing, and deployment. The exact time depends on the complexity of your requirements."}, "2": {"q": "Can I add more than 50 products to my website?", "a": "Absolutely! While the base package includes up to 50 products, we can scale it to fit your needs. Additional products can be added for an extra fee, and we'll make sure everything is easy for you to manage."}, "3": {"q": "Will my website be mobile-friendly?", "a": "Yes, 100%! All our designs are fully responsive, ensuring your site looks and works great on any device, from smartphones to desktops."}, "4": {"q": "Can you integrate custom features like subscription models or ERP systems?", "a": "Yes, custom features can be integrated! These would require additional time and costs, but we'll work closely with you to create exactly what you need."}, "5": {"q": "What happens after the website is live?", "a": "We provide a training session to help you manage your website and optional ongoing support packages for maintenance, updates, or additional features as your business grows."}}}, "pricing_section": {"title": "Pricing", "subtitle": "Choose your Business and Price your project!", "price": "Starting Price", "currency": "AED", "contact_us": "Contact Us", "custom_title": "Custom Solutions", "custom_description": "If you're looking for something more specific , you can provide us with some details and we'll contact you soon"}, "pricing_system": {"title": "E-commerce App Cost", "description": "Customize your store and e-commerce app and track real-time cost", "steps": ["Platform", "Required Features", "Additional Features", "Support", "Marketing"], "invoice": {"price": "Price", "costs": ["Required Features", "Additional Features", "Add Ons", "Team Cost"], "total": "Total Price", "time": "Time Line", "stages": ["Discovery", "UI/UX", "Development", "QA & Delivery"], "total_time": "Total Time", "time_unit": "week", "hours": "hours", "submit": "Submit", "submit_title": "Request Submission", "submit_description": "Please provide your contact information to complete your request"}, "platform": {"title": "Platform", "description": "Choose the targeted platforms for your E-commerce :", "website": "Website", "mobile": "Mobile App"}, "team": {"title": "Required Team", "description": "What is the size of the team you're looking for?"}, "basic": {"title": "Required Features", "description": "Basic features can't be changed or removed"}, "advanced": {"title": "Additional Features", "description": "Customize your plan by selecting or removing features below."}, "addons": {"title": "Add Ons", "description": "Customize your plan by selecting or removing features below.", "support": "Support", "marketing": "Marketing and Branding :"}}, "form": {"name": "Full Name", "name_placeholder": "<PERSON>", "email": "Email Address", "email_placeholder": "<EMAIL>", "phone": "Phone Number", "cancel": "Cancel", "submit": "Submit", "browse": "Browse Requests", "submitting": "Submitting...", "submit_error": "Failed to submit request"}, "portfolioText": {"sectionTitle": "Our Work", "sectionSubtitle": "Digital Solutions That Drive Results", "sectionDescription": "We create tailored software solutions across multiple industries, helping businesses transform their ideas into powerful digital experiences.", "viewProject": "View Project", "viewAllProjects": "View All Projects", "viewAllPortfolio": "View All Portfolio", "projectDetails": "Project Details", "technologies": "Technologies Used", "techStack": "Tech Stack", "keyFeatures": "Key Features", "projectExample": "Project Example", "additionalInfo": "Each category represents our expertise in delivering tailored solutions that meet specific industry needs and client requirements.", "client": "Client", "duration": "Duration", "category": "Category", "challenge": "Challenge", "solution": "Solution", "results": "Results", "categories": {"healthcare": {"name": "Healthcare", "description": "We deliver smart, scalable digital solutions tailored for healthcare providers. From streamlined appointment booking systems and medical center websites to patient engagement tools and team management platforms, our services are designed to enhance operational efficiency, improve patient experience, and support the digital transformation of clinics, hospitals, and wellness centers.", "techStack": ["Next.js", "Bootstrap", "Node.js", "MongoDB"], "keyFeatures": ["Services section", "Team profiles", "Patient testimonials", "Responsive design", "Contact and location info", "Appointment booking section", "SEO optimization"], "projectExample": {"name": "Al-ain Medical Center", "description": "A modern, user-friendly website for a medical center that provides a comprehensive overview of the clinic, details of offered medical services, professional team profiles, patient testimonials, and an integrated appointment booking system."}}, "ecommerce": {"name": "E-Commerce", "description": "We build powerful, customizable e-commerce platforms designed to streamline online selling and delivery operations. From fast deployment and multi-language support to inventory management, payment automation, and marketing tools—our solutions help businesses launch, grow, and scale with confidence.", "techStack": ["Angular", "Flutter", "Node.js", "PostgreSQL"], "keyFeatures": ["Rapid Deployment", "Unified Operations Dashboard", "Built-in Growth Suite", "Multi-Market Ready", "End-to-End Automation", "Mobile-First Experience", "Secure & Compliant"], "projectExample": {"name": "Maradi", "description": "A comprehensive e-commerce and delivery management platform designed to help businesses launch and scale quickly. With a focus on rapid app development, integrated operations, and built-in growth tools like SEO and analytics."}}, "shipment": {"name": "Shipment & Distribution", "description": "We provide innovative, community-driven delivery platforms that connect senders with travelers for faster, more affordable, and secure parcel shipping worldwide. Our solutions offer flexible shipment options, including VIP express services and regular peer-to-peer deliveries, combined with optional indemnity coverage and real-time tracking.", "techStack": ["React Native", "Node.js", "MongoDB", "Socket.io"], "keyFeatures": ["VIP & Emergency Shipments", "Regular Shipments with Travelers", "Mobile App with Smart Matching", "Real-Time Notifications & Tracking", "Traveler Rewards System"], "projectExample": {"name": "Pa<PERSON><PERSON>", "description": "A peer-to-peer delivery platform that connects senders with travelers to ship items faster, cheaper, and more socially than traditional courier services."}}, "epayment": {"name": "E-Payment", "description": "We deliver seamless, secure digital payment platforms that simplify financial management across multiple banks and accounts. Our solutions enable instant balance tracking, effortless fund transfers using just mobile numbers, and an intuitive user experience—eliminating the need for complex banking details.", "techStack": ["Flutter", "Node.js", "PostgreSQL", "Redis"], "keyFeatures": ["Multi-Bank Account Management", "Real-Time Balance Updates", "Mobile Number Transfers", "Seamless & Secure Transfers"], "projectExample": {"name": "<PERSON><PERSON>", "description": "A free all-in-one banking app that lets users link multiple bank accounts, check balances in real time, and make seamless transfers using just a mobile number—no IBANs needed."}}}}, "ecommerce": {"example": "Check our E-commerce", "title": "E-COMMERS PROJECT", "subtitle": "Create Your E-Commerce Store Easily and Start Selling Online!", "description": "We provide a complete e-commerce application that helps you import and sell your products online in a matter of days.", "starting_from": "Starting from", "preview": {"title": "Towards an Exceptional Digital Shopping Experience!", "description": "We specialize in designing and developing e-commerce applications that help your brand stand out and achieve your business goals. Whether you're looking for a complete new store or want to enhance your customers' experience, we provide a high-quality solution that meets industry standards. With our expert team, we'll help you transform your online shopping platform into a high-impact store that exceeds your customers' expectations."}, "features": {"title": "Basic Features", "no_features": "No features available", "items": {"design": {"title": "Significant Design", "description": "Create clean and intuitive user experience to provide seamless shopping experience.", "price": "1000 AED"}, "order": {"title": "Order Management", "description": "Handle orders efficiently with real-time updates, inventory tracking, and order status.", "price": "500 AED"}, "accounts": {"title": "User Accounts & Authentication", "description": "Provide customers secure access to their accounts and purchase history.", "price": "500 AED"}, "cart": {"title": "Cart and Checkout System", "description": "Smooth shopping cart experience with secure payment processing options.", "price": "1200 AED"}, "admin": {"title": "Admin Panel", "description": "Comprehensive dashboard to efficiently manage your e-commerce store.", "price": "1000 AED"}, "inventory": {"title": "Inventory Management", "description": "Track stock levels in real-time with automated alerts and updates.", "price": "500 AED"}}}, "technologies": {"title": "Technologies", "description": "We use the latest global technologies like React.js, Node.js, and advanced web development to develop your software solution. These technologies ensure your website is fast, secure, and ready for future growth.", "items": {"nextjs": "NextJS", "javascript": "JavaScript", "react": "React", "node": "Node JS"}}, "cta": {"choose": "<PERSON><PERSON>", "company": "GoTru", "slogan": "Company to make your dreams a reality!", "start_button": "Start Project", "contact_button": "Contact More", "soon": "Soon..."}}}