{"about": "مَن نحن", "services": "الخدمات", "technologies": "التقنيات", "portfolio": "أعمالنا", "work": "آلية العمل", "contact": "إتصل بنا", "go_tru": "GoTru", "pricing": "الأسعار", "heroHeadline": "فِرَق متخصصة، حلول مُبتَكَرة", "outcome": "فعلية", "delivered": "نتائج", "heroDescription": {"textBeforeBold1": "فرقنا المتخصصة تضمن لك حلولا تقنية", "bold1": "عالية الجودة", "textBetweenBold1Bold2": "بتكلفة", "bold2": "مناسبة،", "textBetweenBold2Bold3": "لتوفير", "bold3": "الكوادر المثالية", "textAfterBold3": "لمشروعك وإنجازه بنجاح، في الوقت المحدد وضمن الميزانية."}, "contactUs": "تواصل معنا", "aboutUs": "لماذا", "aboutUs2": "GoTru ؟", "aboutUsDescription": "بعد رحلة تزيد عن عشر سنوات من العمل والتجربة، تعلمنا أن النجاح لا يعتمد فقط على المعرفة التقنية، بل على الفهم العميق لاحتياجات كل مشروع. لهذا السبب، نحن لا نقدم حلولًا جاهزة، بل نصمم تجارب مصممة خصيصًا لك، تحقق لك نتائج فعلية.     فريقنا من الخبراء يعمل بشغف لتقديم حلول تواكب تطلعاتك، وتساعدك على تحقيق رؤيتك بأفضل الطرق الممكنة. نؤمن بأن الابتكار لا يكتمل إلا عندما يتوافق مع الخبرة.", "missionStatement": {"title": "مهمتنا:", "description": "نؤمن بأن التكنولوجيا ليست مجرد أدوات، بل هي وسيلة لإحداث تغيير حقيقي. مهمتنا هي تطوير حلول برمجية مبتكرة تلبي احتياجات اليوم وتُشكِّل أسس المستقبل، مما يساعد الشركات والأفراد على النمو والتطور بكفاءة."}, "notice": {"title": "إشعار قانوني: يُعتبر 'Gotru' اسمًا تجاريًا تُشغله شركة اليمامة الذهبية للتكنولوجيا (ش.م.م)، المسجلة في دولة الإمارات العربية المتحدة. وتُجرى جميع المعاملات الرسمية تحت الاسم المسجل للشركة."}, "languageSupport": {"title": "اللغة العربية في صميم عملنا", "description": "لغتنا هويتنا، وجزء لا يتجزأ من رؤيتنا. نلتزم بتقديم حلول تقنية تدعم اللغة العربية بجمالها وسلاستها، لنُعزز حضورها في العالم الرقمي بأسلوب يمزج بين الأصالة والتطور."}, "servicesText": {"services": "خدماتنا", "servicesHeadline": "إكتشف معنا ما يحتاجه عملك من خدمات", "appDevelopmentTitle": "تطوير التطبيقات", "appDevelopmentDescription": " تطبيقات iOS و Android بأداء متميز باستخدام Flutter، Swift و Kotlin لتوفير تجربة مستخدم سلسة ومتوافقة مع مختلف الأجهزة.", "webDevelopmentTitle": "تطوير المواقع", "webDevelopmentDescription": "تصميم مواقع ويب عصرية وسريعة الاستجابة باستخدام WordPress و Angular و Vue و React، لضمان تجربة استخدام سلسة وزيادة التفاعل.", "systemDevelopmentTitle": "تطوير الأنظمة", "systemDevelopmentDescription": "بناء أنظمة قوية وقابلة للتوسع لتعزيز كفاءة عملياتك، باستخدام أحدث التقنيات مثل Node.js و PHP و .NET.", "techConsultingTitle": "الإستشارات التقنية", "techConsultingDescription": "تحليل وتخطيط استراتيجي لمساعدتك على تجاوز التحديات التقنية وتحقيق أهدافك الرقمية بكفاءة.", "cloudServicesTitle": "الخدمات السحابية", "cloudServicesDescription": " حلول سحابية آمنة ومرنة تعتمد على AWS و Google Cloud و Azure، لضمان الاستقرار والأمان والتوسع السلس.", "databaseManagementTitle": "إدارة قواعد البيانات", "databaseManagementDescription": " تحسين أداء البيانات وأمانها باستخدام تقنيات SQL و NoSQL، لضمان معالجة فعالة للبيانات الضخمة."}, "worksSection": {"Header": "آلية عملنا", "title": "كيف تتحوَّل الأفكار إلى حلولٍ رقمية", "subheading": "نحو تحويل أفكارك إلى حلول رقمية مبتكرة، نعمل وفق عملية دقيقة ومنهجية تضمن تنفيذ مشروعك بأعلى مستويات الاحترافية في كل مرحلة.", "steps": [{"title": "الإستشارة الأولية", "description": "نضع خطة شاملة لتنفيذ الحلول، مع مراعاة الميزانية والجدول الزمني، لضمان تسليم المشروع في الوقت المحدد وضمن الميزانية."}, {"title": "التخطيط", "description": "وضع الخطة المناسبة لتنفيذ الحلول ضمن الملاءة المالية والجدول الزمني المحدد"}, {"title": "التصميم والتطوير والاختبار", "description": "بعد الموافقة على التصاميم، نبدأ في تنفيذها وتطوير النظام، متبعين أساليب اختبار دقيقة لضمان الجودة والأداء."}, {"title": "النشر والدعم", "description": "بمجرد اجتياز المشروع للاختبارات ورضا العميل، نبدأ في نشر الحلول بأحدث التقنيات، مع توفير دعم مستمر لضمان استقرار النظام ونجاحه على المدى الطويل."}]}, "technologiesText": {"sectionTitle": " تقنياتنا - التكنولوجيا التي تبني نجاحك", "mainTitle": "نؤمن بأن التقنية يجب أن تكون الحل، لا العقبة. لذلك، نجعلها سهلة وفعالة لك، حتى تتمكن من التركيز على نمو أعمالك بينما نعتني نحن بجانب التطوير التقني.", "subtitle": "", "tagline": "", "frontend": {"title": "تطوير الواجهة الأمامية", "description": "نقوم بإنشاء واجهات مستخدم ديناميكية وعصرية باستخدام أحدث تقنيات الواجهة الأمامية لضمان تجربة استخدام سلسة وجذابة."}, "backend": {"title": "تطوير الخلفية", "description": "نعمل على بناء أنظمة خلفية قوية ومرنة تضمن لك أداءً عالٍ، أمانًا محكمًا، وقابلية توسّع تتماشى مع نمو اعمالك."}, "database": {"title": "إدارة قواعد البيانات", "description": "نوفر حلولًا موثوقة لإدارة البيانات، تضمن الأداء العالي، الأمان، وقابلية التوسع."}, "cloud": {"title": "الخدمات السحابية", "description": "قدم حلول سحابية مرنة وآمنة لضمان أداءٍ عالٍ وتوسعٍ سهل لاحتياجاتك التقنية."}, "mobile": {"title": "تطوير تطبيقات الهواتف", "description": "نقدم حلولًا مبتكرة لتطوير تطبيقات الهواتف عبر Flutter لضمان تجربة مستخدم مميزة ومتوافقة مع جميع الأنظمة."}}, "contactUsText": {"title": "معاً نصنع الحاضر والمستقبل", "subtitle": "", "brand": "GoTru", "button": "أرسل لنا ما تفكر فيه عبر واتساب"}, "footer": {"tagline": "حيث تتحوَّل الأفكار الرقمية إلى حقيقة", "contactTitle": "اتصل بنا"}, "seoText": {"title": "GoTru - شركة تطوير البرمجيات والاستشارات التقنية", "description": "Go-Tru - شركة رائدة في تطوير البرمجيات والاستشارات التقنية متخصصة في تطوير الويب والتطبيقات والخدمات السحابية والحلول المتطورة .", "keywords": "تطوير البرمجيات، تطوير التطبيقات، تطوير تطبيقات الجوال، تطوير الويب، الاستشارات التقنية، الخدمات السحابية، إدارة قواعد البيانات، فريق متعدد اللغات، AWS, Google Cloud, Azure, Node.js, PHP, .NET, Flutter, WordPress, Angular, Vue, React, SQL, NoSQL, شركة تقنية في دبي، تطوير برمجيات في الإمارات", "ogTitle": "Go-Tru - شركة تطوير البرمجيات والاستشارات التقنية", "ogDescription": "شركة رائدة في تطوير البرمجيات والاستشارات التقنية متخصصة في تطوير الويب والتطبيقات والخدمات السحابية والحلول ثنائية اللغة.", "twitterTitle": "Go-Tru - شركة تطوير البرمجيات والاستشارات التقنية", "twitterDescription": "شركة رائدة في تطوير البرمجيات والاستشارات التقنية متخصصة في تطوير الويب والتطبيقات والخدمات السحابية والحلول ثنائية اللغة.", "faqTitle": "الأسئلة الشائعة | GoTru", "faqDescription": "اعثر على إجابات للأسئلة الشائعة حول خدمات تطوير البرمجيات من GoTru والأسعار والتقنيات وعمليات المشاريع.", "faqOgTitle": "الأسئلة الشائعة GoTru - أسئلة وأجوبة تطوير البرمجيات", "faqOgDescription": "احصل على إجابات للأسئلة الشائعة حول خدمات تطوير البرمجيات والتقنيات والأسعار وعملية تسليم المشاريع.", "faqTwitterTitle": "الأسئلة الشائعة GoTru - أسئلة وأجوبة تطوير البرمجيات", "faqTwitterDescription": "احصل على إجابات للأسئلة الشائعة حول خدمات تطوير البرمجيات والتقنيات والأسعار وعملية تسليم المشاريع.", "pricing": {"overview_suffix": "احصل على معلومات تسعير مفصلة ونظرة عامة على المشروع مع نموذج التسعير الشفاف من GoTru.", "tool_suffix": "استخدم حاسبة التسعير التفاعلية لتقدير التكاليف والجداول الزمنية لمشروعك."}}, "faq": {"1": {"title": "الأسئلة العامة", "1": {"q": "ما هي GoTru ؟", "a": "GoTru هي وكالة متخصصة في تطوير البرمجيات، تقدم حلولًا رقمية عالية الجودة مُصممة خصيصًا لتناسب احتياجاتك الفريدة. نقدم خدمات مثل تطوير التطبيقات، تطوير المواقع، أنظمة البرمجيات، الاستشارات التقنية، خدمات الحوسبة السحابية، وإدارة قواعد البيانات."}, "2": {"q": "ما هي مهمة GoTru؟", "a": "مهمتنا هي تحويل أفكارك إلى حلول رقمية قوية تدعم نجاح أعمالك."}, "3": {"q": "أين يقع مقر GoTru؟", "a": "يقع مقرنا في دبي، الإمارات العربية المتحدة، مبنى آيريس باي، منطقة بيزنس باي."}, "4": {"q": "كيف يمكنني التواصل مع GoTru؟", "a": "يمكنك التواصل معنا عبر الهاتف على الرقم +971 52 828 1927، أو البريد الإلكتروني <EMAIL>، أو عبر بدء محادثة على واتساب."}, "5": {"q": "هل يدعم فريق GoTru التواصل بلغات متعددة؟", "a": "نعم، فريقنا يتحدث لغات متعددة، بما في ذلك اللغة العربية، لمساعدتك بشكل فعال."}}, "2": {"title": "أسئلة الخدمات", "1": {"q": "ما الخدمات التي تقدمها GoTru؟", "a": "نقدم مجموعة واسعة من الخدمات، تشمل: تطوير التطبيقات (لأنظمة iOS و Android باستخدام Flutter) تطوير المواقع الإلكترونية (باستخدام WordPress، Angular، Vue، React  تطوير الأنظمة البرمجية (باستخدام Node، PHP، .Net) الاستشارات التقنية خدمات الحوسبة السحابية (AWS، Google Cloud، Azure) إدارة قواعد البيانات (SQL و NoSQL)"}, "2": {"q": "ما التقنيات المستخدمة في تطوير التطبيقات؟", "a": "نستخدم إطار عمل Flutter لبناء تطبيقات مبتكرة وسلسة لكل من iOS و Android."}, "3": {"q": "هل يمكن لـ GoTru مساعدتي في تطوير موقع ويب؟", "a": "نعم، نحن متخصصون في تصميم مواقع ويب جذابة وتفاعلية تتكيف مع جميع الشاشات باستخدام تقنيات مثل WordPress، Angular، Vue، و React."}, "4": {"q": "هل تقدمون خدمات الحوسبة السحابية؟", "a": "Yنعم، نقدم حلولًا سحابية آمنة وقابلة للتوسع باستخدام AWS، Google Cloud، و Azure."}, "5": {"q": "ماذا تشمل خدمات الاستشارات التقنية؟", "a": "نقدم استشارات خبيرة لمساعدتك على تجاوز التحديات التقنية، وتحسين استراتيجيتك الرقمية، وتحقيق أهداف عملك."}}, "3": {"title": "أسئلة حول آلية العمل", "1": {"q": "ما هو نهج GoTru في تسليم المشاريع؟", "a": "نتبع عملية مُحكمة لضمان إدارة مشروعك بدقة في كل مرحلة، مع الالتزام بالوقت والميزانية المتفق عليها."}, "2": {"q": "كيف تضمنون اختيار الفريق المناسب لمشروعي ؟", "a": "نختار فرقنا المتخصصة بعناية لتتناسب مع متطلبات مشروعك، مما يضمن نتائج عالية الجودة."}, "3": {"q": "هل تعملون مع قطاعات أو صناعات محددة؟", "a": "نخدم قطاعات متنوعة، لكننا نركز على تقديم حلول رقمية مخصصة تناسب الاحتياجات الفريدة لكل عميل."}, "4": {"q": "", "a": ""}, "5": {"q": "", "a": ""}}, "4": {"title": "أسئلة التقنيات", "1": {"q": "ما تقنيات الواجهات الأمامية (Frontend) التي تستخدمونها؟", "a": "نستخدم تقنيات حديثة مثل WordPress، Angular، Vue، React، Next.js، و Nuxt.js لبناء واجهات تفاعلية ديناميكية."}, "2": {"q": "ما تقنيات الواجهات الخلفية (Backend) التي تتخصصون فيها؟", "a": "نتميز في بناء أنظمة قوية وقابلة للتوسع باستخدام Node.js، PHP، و .Net."}, "3": {"q": "هل تديرون قواعد البيانات؟", "a": "نعم، ننفذ حلولًا فعالة لقواعد البيانات باستخدام تقنيات SQL و NoSQL."}, "4": {"q": "", "a": ""}, "5": {"q": "", "a": ""}}, "5": {"title": "أسئلة العمل والتعاون", "1": {"q": "كيف أبدأ مشروعًا مع GoTru؟", "a": "يمكنك التواصل معنا عبر الهاتف أو البريد الإلكتروني أو واتساب لمناقشة متطلبات مشروعك وبدء التعاون."}, "2": {"q": "هل تعملون مع عملاء دوليين ؟", "a": "نعم، نتعامل مع عملاء من حول العالم ونقدم حلولًا مخصصة تناسب احتياجاتهم."}, "3": {"q": "ما الذي يميز GoTru عن الوكالات الأخرى؟", "a": "نتميز بفرق متخصصة، وتكاليف مُحسنة، والتزامنا بتسليم نتائج عالية الجودة في الوقت والميزانية المحددين."}, "4": {"q": "هل يمكنكم إدارة المشاريع كبيرة الحجم؟", "a": "نعم، لدينا الخبرة والموارد اللازمة لإدارة المشاريع الكبيرة مع ضمان الكفاءة والقابلية للتوسع."}, "5": {"q": "", "a": ""}}, "6": {"title": "أسئلة التجارة الإلكترونية", "1": {"q": "كم تستغرق عملية إطلاق موقع التجارة الإلكتروني؟", "a": "في المتوسط، تستغرق العملية 4 أسابيع من الاجتماع التأسيسي حتى الإطلاق. يشمل هذا التصميم، التطوير، الاختبار، والنشر. قد يختلف الوقت حسب تعقيد المتطلبات."}, "2": {"q": "هل يمكنني إضافة أكثر من 50 منتجًا إلى موقعي ؟", "a": "بالتأكيد! الباقة الأساسية تشمل حتى 50 منتجًا، لكن يمكننا توسيعها حسب احتياجاتك. مقابل رسوم إضافية، سنسهل عليك إدارة أي عدد من المنتجات."}, "3": {"q": " هل سيكون موقعي متوافقًا مع أجهزة الجوال؟", "a": "نعم، 100%! جميع تصاميمنا متجاوبة بالكامل، مما يضمن ظهور موقعك وعمله بشكل ممتاز على جميع الأجهزة."}, "4": {"q": "هل يمكنكم دمج ميزات مخصصة مثل أنظمة الاشتراكات أو أنظمة ERP؟", "a": "نعم، يمكننا دمج الميزات المخصصة! قد تتطلب وقتًا و تكلفة إضافية، لكننا نعمل معك لتحقيق ما تحتاجه بالضبط."}, "5": {"q": "ماذا يحدث بعد إطلاق الموقع؟", "a": "نقدم جلسة تدريبية لمساعدتك على إدارة موقعك، بالإضافة إلى باقات دعم مستمرة اختيارية للصيانة، التحديثات، أو إضافة ميزات جديدة مع نمو عملك."}}}, "pricing_section": {"title": "الأسعار", "subtitle": "اختر مجال عملك وقم بتسعير مشروعك!", "price": "السعر المبدئي", "currency": "د.إ", "contact_us": "تواصل معنا", "custom_title": "حلول مخصصة", "custom_description": "إذا كنت تبحث عن شيء أكثر تحديدًا، يمكنك تزويدنا ببعض التفاصيل وسنقوم بالتواصل معك قريبًا."}, "pricing_system": {"title": "تكلفة متجر إلكتروني", "description": "قم بتخصيص متجرك الإلكتروني و تعرف على الأسعار بشكل لحظي", "steps": ["المنصة", "الميزات المطلوبة", "الميزات المتقدمة", "الدعم", "التسويق"], "invoice": {"price": "السعر", "costs": ["الميزات الأساسية", "ميزات متقدمة", "إضافات", "تكلفة الفريق"], "total": "التكلفة الإجمالية", "time": "المسار الزمني", "stages": ["اكتشاف", "UI/UX", "تطوير", "اختبار و تسليم"], "total_time": "الوقت الكلي", "time_unit": "أسابيع", "hours": "ساعات", "submit": "إرسال", "submit_title": "إرسال طلب", "submit_description": "يرجى تزويدنا بمعلومات التواصل معك كي نكمل طلبك"}, "platform": {"title": "المنصة", "description": "إختر المنصات المستهدفة لتطبيقك :", "website": "موقع الكتروني", "mobile": "تطبيق هاتف"}, "team": {"title": "الفريق المطلوب", "description": "ما هو حجم الفريق الذي تبحث عنه ؟"}, "basic": {"title": "الميزات المطلوبة", "description": "الميزات الأساسية لا يمكن إزالتها أو التعديل عليها"}, "advanced": {"title": "الميزات المتقدمة", "description": "بإمكانك التعديل على الميزات عن طريق اختيار أو ازالة الميزات من الأسفل"}, "addons": {"title": "الإضافات", "description": "بإمكانك التعديل على الميزات عن طريق اختيار أو ازالة الميزات من الأسفل", "support": "الدعم", "marketing": "التسويق والعلامة التجارية"}}, "form": {"name": "الاسم الكامل", "name_placeholder": "<PERSON>", "email": "الب<PERSON>يد الإلكتروني", "email_placeholder": "<EMAIL>", "phone": "رقم الهاتف", "cancel": "Cancel", "submit": "إرسال", "browse": "تص<PERSON><PERSON> الطلبات", "submitting": "الرجاء الانتظار...", "submit_error": "!لم يتم إرسال الطلب"}, "portfolioText": {"sectionTitle": "أعمالنا", "sectionSubtitle": "حلول رقمية تحقق النتائج", "sectionDescription": "نطور حلول برمجية مخصصة عبر صناعات متعددة، نساعد الشركات على تحويل أفكارها إلى تجارب رقمية قوية.", "viewProject": "عرض المشروع", "viewAllProjects": "عرض جميع المشاريع", "viewAllPortfolio": "عرض جميع الأعمال", "projectDetails": "تفاصيل المشروع", "technologies": "التقنيات المستخدمة", "techStack": "التقنيات المستخدمة", "keyFeatures": "الميزات الرئيسية", "projectExample": "مثال على المشروع", "additionalInfo": "كل فئة تمثل خبرتنا في تقديم حلول مخصصة تلبي احتياجات الصناعة المحددة ومتطلبات العملاء.", "client": "العميل", "duration": "المدة الزمنية", "category": "الفئة", "challenge": "التحدي", "solution": "الحل", "results": "النتائج", "categories": {"healthcare": {"name": "الرعاية الصحية", "description": "نقدم حلولاً رقمية ذكية وقابلة للتوسع مصممة خصيصاً لمقدمي الرعاية الصحية. من أنظمة حجز المواعيد المبسطة ومواقع المراكز الطبية إلى أدوات إشراك المرضى ومنصات إدارة الفرق، خدماتنا مصممة لتعزيز الكفاءة التشغيلية وتحسين تجربة المرضى ودعم التحول الرقمي للعيادات والمستشفيات ومراكز العافية.", "techStack": ["Next.js", "Bootstrap", "Node.js", "MongoDB"], "keyFeatures": ["قسم الخدمات", "ملفات الفريق", "شهادات المرضى", "تصميم متجاوب", "معلومات الاتصال والموقع", "قسم حجز المواعيد", "تحسين محركات البحث"], "projectExample": {"name": "مركز العين الطبي", "description": "موقع ويب حديث وسهل الاستخدام لمركز طبي يوفر نظرة شاملة على العيادة وتفاصيل الخدمات الطبية المقدمة وملفات الفريق المهني وشهادات المرضى ونظام حجز مواعيد متكامل."}}, "ecommerce": {"name": "التجارة الإلكترونية", "description": "نبني منصات تجارة إلكترونية قوية وقابلة للتخصيص مصممة لتبسيط عمليات البيع والتوصيل عبر الإنترنت. من النشر السريع والدعم متعدد اللغات إلى إدارة المخزون وأتمتة المدفوعات وأدوات التسويق - تساعد حلولنا الشركات على الإطلاق والنمو والتوسع بثقة.", "techStack": ["Angular", "Flutter", "Node.js", "PostgreSQL"], "keyFeatures": ["النشر السريع", "لوحة تحكم العمليات الموحدة", "مجموعة النمو المدمجة", "جاهز لعدة أسواق", "الأتمتة الشاملة", "تجربة الهاتف المحمول أولاً", "آمن ومتوافق"], "projectExample": {"name": "مرادي", "description": "منصة شاملة للتجارة الإلكترونية وإدارة التوصيل مصممة لمساعدة الشركات على الإطلاق والتوسع بسرعة. مع التركيز على تطوير التطبيقات السريع والعمليات المتكاملة وأدوات النمو المدمجة مثل تحسين محركات البحث والتحليلات."}}, "shipment": {"name": "الشحن والتوزيع", "description": "نوفر منصات توصيل مبتكرة مدفوعة بالمجتمع تربط المرسلين بالمسافرين لشحن الطرود بشكل أسرع وأكثر اقتصادية وأماناً في جميع أنحاء العالم. تقدم حلولنا خيارات شحن مرنة، بما في ذلك خدمات VIP السريعة والتوصيل العادي من نظير إلى نظير، مع تغطية تعويضية اختيارية وتتبع في الوقت الفعلي.", "techStack": ["React Native", "Node.js", "MongoDB", "Socket.io"], "keyFeatures": ["شحنات VIP والطوارئ", "الشحنات العادية مع المسافرين", "تطبيق الهاتف المحمول مع المطابقة الذكية", "الإشعارات والتتبع في الوقت الفعلي", "نظام مكافآت المسافرين"], "projectExample": {"name": "باكيك", "description": "منصة توصيل من نظير إلى نظير تربط المرسلين بالمسافرين لشحن العناصر بشكل أسرع وأرخص وأكثر اجتماعية من خدمات البريد السريع التقليدية."}}, "epayment": {"name": "الدفع الإلكتروني", "description": "نقدم منصات دفع رقمية سلسة وآمنة تبسط الإدارة المالية عبر عدة بنوك وحسابات. تمكن حلولنا من تتبع الرصيد الفوري وتحويل الأموال بسهولة باستخدام أرقام الهواتف المحمولة فقط وتجربة مستخدم بديهية - مما يلغي الحاجة إلى تفاصيل مصرفية معقدة.", "techStack": ["Flutter", "Node.js", "PostgreSQL", "Redis"], "keyFeatures": ["إدارة حسابات متعددة البنوك", "تحديثات الرصيد في الوقت الفعلي", "تحويلات برقم الهاتف المحمول", "تحويلات سلسة وآمنة"], "projectExample": {"name": "سليم باي", "description": "تطبيق مصرفي مجاني شامل يتيح للمستخدمين ربط حسابات مصرفية متعددة والتحقق من الأرصدة في الوقت الفعلي وإجراء تحويلات سلسة باستخدام رقم الهاتف المحمول فقط - دون الحاجة إلى أرقام IBAN."}}}}, "ecommerce": {"example": "شاهد بعض أعمالنا", "title": "مشروع متجر إلكتروني", "subtitle": "أنشئ متجرك الإلكتروني بسهولة وابدأ البيع عبر الإنترنت!", "description": "نقدم تطبيق تجارة إلكترونية متكامل يساعدك على استيراد وبيع منتجاتك عبر الإنترنت في غضون أيام.", "starting_from": "ابتداءً من", "preview": {"title": "نحو تجربة تسوق رقمية استثنائية!", "description": "نحن متخصصون في تصميم وتطوير تطبيقات التجارة الإلكترونية التي تساعد علامتك التجارية على التميز وتحقيق أهداف عملك. سواء كنت تبحث عن متجر جديد بالكامل أو ترغب في تحسين تجربة عملائك، فإننا نقدم حلاً عالي الجودة يلبي معايير الصناعة. مع فريقنا من الخبراء، سنساعدك على تحويل منصة التسوق عبر الإنترنت الخاصة بك إلى متجر عالي التأثير يتجاوز توقعات عملائك."}, "features": {"title": "الميزات الأساسية", "no_features": "لا توجد ميزات متاحة", "items": {"design": {"title": "تصميم مميز", "description": "إنشاء تجربة مستخدم نظيفة وبديهية لتوفير تجربة تسوق سلسة.", "price": "1000 درهم"}, "order": {"title": "إدارة الطلبات", "description": "التعامل مع الطلبات بكفاءة مع تحديثات في الوقت الفعلي وتتبع المخزون وحالة الطلب.", "price": "500 درهم"}, "accounts": {"title": "حسابات المستخدمين والمصادقة", "description": "توفير وصول آمن للعملاء إلى حساباتهم وسجل المشتريات.", "price": "500 درهم"}, "cart": {"title": "نظام عربة التسوق والدفع", "description": "تجربة سلسة لعربة التسوق مع خيارات معالجة دفع آمنة.", "price": "1200 درهم"}, "admin": {"title": "لوحة الإدارة", "description": "لوحة تحكم شاملة لإدارة متجرك الإلكتروني بكفاءة.", "price": "1000 درهم"}, "inventory": {"title": "إدارة المخزون", "description": "تتبع مستويات المخزون في الوقت الفعلي مع تنبيهات وتحديثات آلية.", "price": "500 درهم"}}}, "technologies": {"title": "التقنيات", "description": "نستخدم أحدث التقنيات العالمية مثل React.js و Node.js وتطوير الويب المتقدم لتطوير الحلول الرقمية. تضمن هذه التقنيات أن يكون موقعك سريعًا وآمنًا وجاهزًا للنمو المستقبلي.", "items": {"nextjs": "نكست جي إس", "javascript": "جافاسكريبت", "react": "رياكت", "node": "Node JS"}}, "cta": {"choose": "اختر", "company": "GoTru", "slogan": "لنجعل أفكارك واقعا ملموسا", "start_button": "ابدأ المشروع", "contact_button": "تواصل معنا", "soon": "قريباً..."}}}