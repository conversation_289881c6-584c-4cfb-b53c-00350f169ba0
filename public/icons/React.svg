<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 540 540)" id="40b55a31-ef77-4021-92bf-44a1405a41fa"  >
</g>
<g transform="matrix(1 0 0 1 540 540)" id="a601b00f-8acd-46a3-9a5e-1ac024793d89"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1.04 0 0 1.04 559.05 540)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(0.13 0 0 -0.13 -13.11 -12.18)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: #32748c; fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-4995.56, -4124.11)" d="M 3205 7766 C 2863 7707 2623 7457 2529 7060 C 2518 7013 2504 6962 2499 6945 C 2494 6929 2484 6832 2478 6732 C 2466 6542 2477 6193 2500 6070 C 2505 6040 2517 5972 2525 5920 C 2544 5804 2568 5682 2585 5615 C 2609 5525 2612 5484 2598 5472 C 2590 5466 2551 5450 2510 5436 C 2394 5397 2347 5380 2325 5370 C 2314 5365 2266 5345 2219 5326 C 1883 5190 1541 4975 1364 4789 C 1296 4717 1190 4581 1190 4566 C 1190 4563 1177 4539 1161 4513 C 1084 4388 1045 4174 1069 4006 C 1115 3684 1382 3364 1810 3117 C 1897 3067 2094 2969 2179 2934 C 2226 2914 2274 2894 2285 2889 C 2316 2875 2479 2816 2542 2795 C 2574 2784 2602 2769 2605 2760 C 2608 2751 2606 2724 2601 2699 C 2519 2313 2521 2326 2510 2240 C 2505 2199 2495 2129 2489 2085 C 2474 1985 2473 1435 2488 1365 C 2494 1338 2503 1288 2509 1254 C 2548 1038 2654 825 2793 687 C 2853 626 2974 540 2998 540 C 3002 540 3018 533 3034 525 C 3188 444 3587 457 3795 549 C 3806 554 3835 566 3860 575 C 4005 632 4191 734 4370 854 C 4459 913 4592 1011 4610 1030 C 4613 1033 4638 1053 4665 1075 C 4693 1096 4717 1117 4720 1120 C 4723 1123 4745 1143 4770 1164 C 4795 1185 4840 1226 4870 1254 C 5000 1374 4986 1365 5015 1346 C 5029 1337 5092 1283 5154 1227 C 5299 1096 5339 1063 5485 954 C 5607 863 5697 800 5706 800 C 5709 800 5726 789 5743 776 C 5761 763 5802 737 5835 720 C 5868 702 5918 675 5947 659 C 6076 589 6272 518 6413 490 C 6547 463 6736 463 6845 490 C 6945 514 6969 524 7054 574 C 7188 652 7295 770 7371 925 C 7431 1044 7452 1111 7501 1330 C 7518 1407 7525 1889 7511 2015 C 7484 2251 7471 2341 7448 2440 C 7442 2465 7432 2514 7425 2550 C 7417 2586 7405 2646 7397 2685 C 7389 2723 7384 2760 7387 2767 C 7390 2773 7406 2782 7423 2786 C 7440 2789 7461 2796 7470 2801 C 7478 2805 7512 2818 7545 2829 C 8055 3000 8524 3295 8733 3578 C 8797 3664 8855 3768 8883 3845 C 8891 3867 8903 3901 8910 3920 C 8917 3939 8925 4016 8927 4091 C 8932 4208 8929 4238 8911 4311 C 8886 4407 8828 4533 8767 4623 C 8712 4704 8527 4890 8425 4966 C 8342 5028 8252 5090 8245 5090 C 8242 5090 8211 5108 8176 5129 C 8123 5162 7897 5275 7815 5311 C 7739 5343 7611 5393 7530 5420 C 7445 5449 7380 5476 7380 5483 C 7380 5484 7386 5514 7394 5550 C 7445 5782 7471 5921 7502 6135 C 7520 6259 7520 6811 7502 6900 C 7475 7030 7457 7106 7444 7140 C 7437 7159 7428 7186 7424 7200 C 7411 7247 7329 7405 7291 7455 C 7245 7515 7148 7610 7100 7642 C 6985 7718 6887 7753 6742 7771 C 6657 7782 6623 7781 6509 7767 C 6435 7758 6362 7746 6345 7740 C 6329 7735 6272 7717 6220 7699 C 6062 7648 5828 7528 5645 7406 C 5482 7297 5242 7103 5065 6938 C 5038 6912 5006 6890 4995 6890 C 4984 6890 4944 6920 4905 6958 C 4780 7079 4530 7279 4338 7413 C 4300 7439 4268 7460 4265 7460 C 4262 7460 4241 7472 4217 7487 C 4141 7536 4032 7593 3895 7655 C 3675 7755 3401 7799 3205 7766 z M 3585 7475 C 3723 7434 3921 7348 4030 7282 C 4049 7270 4072 7257 4080 7253 C 4134 7227 4390 7046 4470 6976 C 4490 6959 4520 6934 4537 6920 C 4640 6837 4790 6693 4790 6678 C 4790 6669 4775 6646 4758 6627 C 4701 6569 4627 6487 4570 6420 C 4540 6384 4513 6353 4510 6350 C 4488 6330 4303 6098 4211 5975 C 4037 5740 4056 5757 3964 5749 C 3856 5739 3671 5712 3500 5683 C 3453 5675 3393 5665 3365 5661 C 3338 5656 3297 5648 3275 5642 C 3253 5636 3197 5624 3150 5615 C 3103 5605 3041 5591 3011 5582 C 2982 5574 2939 5566 2917 5566 C 2871 5565 2879 5548 2839 5725 C 2725 6226 2711 6727 2803 7045 C 2835 7156 2908 7289 2975 7355 C 3037 7417 3133 7473 3210 7491 C 3272 7505 3519 7495 3585 7475 z M 6840 7474 C 6939 7434 7010 7377 7075 7286 C 7128 7210 7179 7099 7200 7010 C 7243 6830 7251 6739 7246 6485 C 7241 6222 7219 6016 7176 5825 C 7161 5762 7135 5643 7126 5598 C 7118 5560 7092 5556 6993 5580 C 6913 5599 6849 5614 6735 5641 C 6713 5646 6670 5654 6640 5659 C 6610 5664 6556 5673 6520 5679 C 6363 5707 6276 5720 6145 5735 C 6068 5744 5991 5754 5973 5756 C 5946 5760 5930 5776 5874 5853 C 5753 6018 5524 6306 5414 6430 C 5397 6449 5342 6511 5292 6566 C 5225 6640 5203 6672 5207 6686 C 5211 6702 5321 6807 5410 6881 C 5424 6892 5447 6912 5461 6924 C 5500 6959 5728 7132 5785 7170 C 5980 7300 6173 7399 6334 7452 C 6464 7494 6498 7499 6655 7496 C 6763 7493 6805 7489 6840 7474 z M 5111 6360 C 5284 6169 5542 5850 5554 5812 L 5561 5790 L 5448 5797 C 5302 5807 4664 5807 4538 5797 C 4450 5790 4440 5791 4440 5806 C 4440 5827 4540 5962 4675 6121 C 4691 6140 4716 6170 4729 6186 C 4872 6357 4982 6471 5000 6468 C 5009 6466 5059 6418 5111 6360 z M 5510 5519 C 5639 5513 5757 5504 5771 5498 C 5818 5481 6136 4978 6355 4575 C 6428 4441 6580 4135 6580 4122 C 6580 4109 6388 3719 6370 3695 C 6366 3690 6343 3649 6320 3605 C 6296 3561 6268 3512 6258 3495 C 6248 3479 6237 3458 6233 3450 C 6222 3427 6033 3113 5992 3050 C 5851 2832 5796 2754 5782 2749 C 5772 2746 5704 2737 5630 2729 C 5431 2709 4492 2712 4322 2733 C 4254 2741 4197 2750 4196 2752 C 4195 2754 4160 2807 4119 2870 C 3993 3061 3899 3210 3850 3295 C 3655 3632 3624 3690 3503 3929 L 3406 4123 L 3503 4314 C 3636 4576 3756 4797 3810 4880 C 3816 4888 3825 4904 3830 4915 C 3836 4926 3849 4949 3860 4965 C 3871 4982 3884 5004 3890 5015 C 3896 5026 3929 5080 3965 5135 C 4000 5190 4046 5262 4067 5295 C 4087 5328 4128 5387 4157 5427 C 4200 5486 4215 5500 4242 5504 C 4273 5509 4644 5528 4830 5534 C 4948 5538 5232 5532 5510 5519 z M 3830 5439 C 3830 5433 3813 5402 3792 5371 C 3761 5326 3499 4890 3459 4817 C 3418 4743 3379 4670 3333 4580 C 3302 4522 3275 4474 3271 4472 C 3256 4467 3233 4507 3195 4605 C 3174 4660 3153 4709 3148 4715 C 3144 4720 3140 4731 3140 4740 C 3140 4748 3133 4768 3125 4784 C 3117 4799 3110 4816 3110 4820 C 3110 4825 3105 4841 3099 4857 C 3045 4990 2969 5225 2967 5265 C 2965 5291 2969 5295 3000 5301 C 3019 5305 3076 5318 3125 5329 C 3277 5365 3488 5407 3585 5421 C 3624 5426 3669 5434 3685 5439 C 3729 5453 3830 5452 3830 5439 z M 6330 5436 C 6371 5428 6450 5414 6505 5405 C 6560 5396 6619 5385 6635 5380 C 6652 5376 6683 5369 6705 5366 C 6748 5359 6845 5337 6925 5315 C 6953 5308 6987 5299 7003 5296 C 7021 5292 7030 5283 7030 5270 C 7030 5249 6928 4936 6904 4883 C 6896 4866 6890 4847 6890 4841 C 6890 4835 6884 4816 6876 4798 C 6868 4780 6847 4729 6830 4685 C 6770 4531 6741 4470 6727 4470 C 6720 4470 6707 4489 6696 4511 C 6680 4546 6624 4651 6490 4895 C 6454 4961 6243 5310 6197 5379 C 6177 5409 6163 5437 6166 5442 C 6174 5454 6246 5452 6330 5436 z M 2703 5163 C 2711 5142 2726 5100 2736 5070 C 2745 5040 2759 4999 2766 4980 C 2773 4961 2789 4914 2801 4875 C 2813 4837 2827 4801 2832 4796 C 2836 4790 2840 4779 2840 4771 C 2840 4762 2846 4743 2854 4728 C 2862 4713 2871 4688 2875 4671 C 2879 4654 2886 4638 2891 4635 C 2896 4631 2900 4622 2900 4614 C 2900 4606 2909 4578 2921 4552 C 2946 4494 2946 4493 3010 4340 C 3039 4271 3066 4211 3070 4205 C 3074 4200 3083 4178 3090 4157 C 3104 4113 3107 4124 3010 3905 C 2997 3876 2966 3802 2946 3750 C 2936 3725 2924 3696 2919 3685 C 2895 3625 2851 3510 2816 3415 C 2785 3329 2725 3144 2716 3109 C 2713 3094 2702 3073 2692 3060 L 2674 3038 L 2610 3059 C 2574 3071 2536 3084 2525 3089 C 2514 3094 2478 3107 2445 3120 C 2412 3132 2371 3148 2353 3156 C 2336 3164 2319 3170 2315 3170 C 2301 3170 2000 3320 1934 3360 C 1625 3548 1423 3759 1355 3965 C 1328 4046 1328 4192 1354 4273 C 1421 4484 1622 4695 1925 4876 C 2060 4956 2268 5057 2389 5102 C 2420 5114 2456 5127 2470 5133 C 2543 5163 2648 5198 2666 5199 C 2681 5200 2691 5190 2703 5163 z M 7371 5189 C 7390 5183 7419 5172 7437 5164 C 7454 5156 7473 5150 7479 5150 C 7485 5150 7504 5144 7522 5136 C 7540 5128 7607 5100 7670 5074 C 8139 4879 8489 4612 8606 4360 C 8661 4243 8677 4085 8644 3984 C 8603 3856 8550 3770 8441 3657 C 8223 3430 7891 3239 7436 3079 C 7313 3035 7297 3037 7281 3098 C 7257 3186 7193 3376 7145 3500 C 7138 3519 7111 3589 7085 3655 C 7060 3721 7033 3789 7026 3805 C 7018 3822 7002 3860 6990 3890 C 6968 3945 6919 4055 6898 4097 C 6886 4121 6887 4122 6990 4355 C 7047 4482 7149 4746 7195 4883 C 7214 4942 7234 5000 7239 5012 C 7244 5025 7258 5066 7270 5105 C 7301 5205 7307 5210 7371 5189 z M 6788 3663 C 6853 3507 6933 3295 6950 3235 C 6955 3219 6969 3174 6983 3135 C 7033 2992 7038 2971 7025 2960 C 7008 2946 6837 2902 6675 2870 C 6509 2837 6225 2791 6183 2790 C 6169 2790 6165 2836 6178 2842 C 6190 2847 6470 3301 6470 3315 C 6470 3317 6489 3351 6513 3392 C 6537 3432 6567 3485 6579 3510 C 6592 3535 6615 3580 6632 3610 C 6648 3640 6675 3691 6691 3723 C 6714 3768 6724 3779 6734 3771 C 6741 3765 6765 3716 6788 3663 z M 3320 3688 C 3467 3405 3662 3068 3784 2885 C 3832 2812 3838 2783 3805 2788 C 3631 2812 3587 2819 3375 2861 C 3060 2923 2960 2950 2960 2973 C 2960 2988 3048 3256 3080 3340 C 3092 3370 3105 3406 3110 3420 C 3124 3462 3196 3645 3210 3675 C 3215 3686 3226 3712 3234 3733 C 3242 3753 3255 3770 3263 3770 C 3271 3770 3296 3733 3320 3688 z M 2939 2680 C 2949 2675 2990 2664 3031 2655 C 3072 2646 3123 2634 3145 2629 C 3345 2583 3561 2546 3800 2515 C 4068 2480 4053 2484 4075 2454 C 4086 2439 4122 2390 4155 2345 C 4252 2213 4369 2062 4429 1991 C 4460 1955 4495 1913 4507 1898 C 4533 1864 4645 1738 4727 1651 C 4791 1583 4799 1566 4777 1540 C 4763 1522 4574 1352 4504 1294 C 4218 1057 3889 862 3655 790 C 3414 716 3223 722 3078 807 C 2761 996 2666 1526 2804 2345 C 2824 2465 2871 2663 2882 2678 C 2894 2692 2915 2693 2939 2680 z M 7110 2676 C 7134 2647 7185 2399 7226 2110 C 7235 2052 7244 1896 7247 1764 C 7254 1451 7233 1300 7155 1105 C 7079 916 6978 818 6790 755 C 6730 735 6552 735 6468 755 C 6366 780 6287 803 6260 817 C 6246 824 6232 830 6227 830 C 6223 830 6176 851 6122 877 C 5947 962 5728 1102 5554 1242 C 5372 1389 5200 1546 5200 1566 C 5200 1579 5211 1593 5299 1685 C 5346 1734 5448 1851 5525 1945 C 5547 1972 5579 2010 5595 2029 C 5647 2090 5704 2162 5728 2198 C 5741 2217 5758 2239 5766 2248 C 5775 2256 5789 2275 5798 2289 C 5827 2335 5931 2469 5943 2477 C 5950 2481 5982 2488 6015 2492 C 6048 2495 6116 2503 6165 2509 C 6215 2515 6280 2524 6310 2529 C 6340 2534 6412 2546 6470 2555 C 6528 2564 6604 2578 6640 2586 C 6676 2593 6728 2604 6755 2609 C 6783 2614 6834 2626 6870 2634 C 6906 2643 6960 2655 6990 2661 C 7020 2667 7052 2676 7060 2681 C 7082 2693 7096 2692 7110 2676 z M 5560 2435 C 5560 2413 5233 2013 5073 1840 C 5003 1764 4993 1761 4956 1803 C 4939 1822 4895 1871 4859 1911 C 4716 2070 4669 2126 4526 2305 C 4474 2371 4431 2430 4430 2435 C 4430 2442 4625 2445 4995 2445 C 5371 2445 5560 2442 5560 2435 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -2.51 -2.36)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: #32748c; fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-4997.7, -4120.85)" d="M 4845 4948 C 4652 4900 4536 4840 4419 4728 C 4248 4564 4160 4356 4160 4119 C 4160 3620 4596 3232 5093 3289 C 5323 3315 5528 3435 5669 3623 C 5884 3909 5891 4295 5688 4596 C 5613 4707 5464 4832 5340 4888 C 5295 4908 5225 4929 5137 4949 C 5080 4962 4901 4961 4845 4948 z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>