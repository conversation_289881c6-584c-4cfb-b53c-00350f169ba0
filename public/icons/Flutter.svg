<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 540 540)" id="2caa7cbc-b49e-4f03-8e1b-56770f8f558d"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="e86f53c6-9899-4058-a9e4-8b7253af523d"  >
</g>
<g transform="matrix(0.42 0 0 0.42 500.53 540)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(1 0 0 1 0 -333.15)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(50,116,140); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-999.5, -903.95)" d="M 381 1618 L 0 1237 L 1237.1 0 L 1999 0 M 1999 1141.5 L 1237.1 1141.5 L 951.6999999999999 1426.9 L 1332.6999999999998 1807.9" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 475.85 903.95)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(50,116,140); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-1475.35, -2141.05)" d="M 951.7 2188.8 L 1237.1 2474.2000000000003 L 1999 2474.2000000000003 L 1332.7 1807.9000000000003" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -47.5 570.95)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(50,116,140); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-952, -1808.05)" d="M 571.6 1808.1 L 952 1427.6 L 1332.4 1808 L 952.0000000000001 2188.5 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 169.25 787.75)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(50,116,140); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-1168.75, -2024.85)" d="M 952 2188.5 L 1332.4 1808.1 L 1385.5 1861.1999999999998 L 1005.1 2241.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 234.85 761.2)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(50,116,140); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-1234.35, -1998.3)" d="M 951.7 2188.8 L 1517 1993.5000000000002 L 1332.7 1807.8000000000002" stroke-linecap="round" />
</g>
</g>
</g>
<g transform="matrix(NaN NaN NaN NaN 0 0)"  >
<g style=""   >
</g>
</g>
</svg>