<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 540 540)" id="2caa7cbc-b49e-4f03-8e1b-56770f8f558d"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="e86f53c6-9899-4058-a9e4-8b7253af523d"  >
</g>
<g transform="matrix(NaN NaN NaN NaN 0 0)"  >
<g style=""   >
</g>
</g>
<g transform="matrix(0.37 0 0 0.37 540 510.44)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(1 0 0 1 -146.89 -615.96)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(50,116,140); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-1046, -344.02)" d="M 1513.8 528.7 L 1586.6 528.7 L 1794 321.30000000000007 L 1804.2 233.30000000000007 C 1418.3000000000002 -107.29999999999995 829.2 -70.59999999999991 488.60000000000014 315.30000000000007 C 393.9 422.5 325.2 550 287.8 688 C 310.90000000000003 678.5 336.5 677 360.6 683.6 L 775.3 615.2 C 775.3 615.2 796.4 580.3000000000001 807.3 582.5 C 991.8 379.9 1302.3 356.3 1515.3 528.7 L 1513.8 528.7 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 568.43 116.98)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(50,116,140); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-1761.31, -1076.96)" d="M 2089.4 688 C 2041.7 512.5 1943.9 354.7 1807.8000000000002 234 L 1516.8000000000002 525 C 1639.7000000000003 625.4 1709.7000000000003 776.7 1706.7000000000003 935.4 L 1706.7000000000003 987.1 C 1849.8000000000002 987.1 1965.7000000000003 1103.1 1965.7000000000003 1246.1 C 1965.7000000000003 1389.1999999999998 1849.7000000000003 1505.1 1706.7000000000003 1505.1 L 1188.6000000000004 1505.1 L 1136.9000000000003 1557.5 L 1136.9000000000003 1868.2 L 1188.6000000000004 1919.9 L 1706.7000000000003 1919.9 C 2003.7000000000003 1922.2 2267.2000000000003 1729.7 2355.4000000000005 1446.1000000000001 C 2443.4 1162.4 2335.4 854.4 2089.4 688 L 2089.4 688 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -467.44 738.02)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(50,116,140); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-725.45, -1698)" d="M 669.8 1917 L 1187.9 1917 L 1187.9 1502.3 L 669.8 1502.3 C 632.9 1502.3 596.4 1494.3999999999999 562.8 1479 L 489.99999999999994 1501.5 L 281.19999999999993 1708.9 L 262.99999999999994 1781.7 C 380.1 1870.1 523 1917.6 669.8 1917 L 669.8 1917 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -589.46 215.51)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(50,116,140); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-603.42, -1175.5)" d="M 669.8 571.6 C 381.99999999999994 573.3000000000001 127.09999999999991 757.6 35.299999999999955 1030.3 C -56.50000000000004 1303 34.99999999999996 1604 263.09999999999997 1779.4 L 563.5999999999999 1478.9 C 433.19999999999993 1420 375.2999999999999 1266.6000000000001 434.19999999999993 1136.2 C 493.0999999999999 1005.8000000000001 646.5 947.9000000000001 776.8999999999999 1006.8000000000001 C 834.2999999999998 1032.8000000000002 880.2999999999998 1078.8000000000002 906.2999999999998 1136.2 L 1206.7999999999997 835.7 C 1078.9 668.6 880.2 570.9 669.8 571.6 L 669.8 571.6 z" stroke-linecap="round" />
</g>
</g>
</g>
<g transform="matrix(NaN NaN NaN NaN 0 0)"  >
<g style=""   >
</g>
</g>
</svg>