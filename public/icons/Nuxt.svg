<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 540 540)" id="40b55a31-ef77-4021-92bf-44a1405a41fa"  >
</g>
<g transform="matrix(1 0 0 1 540 540)" id="a601b00f-8acd-46a3-9a5e-1ac024793d89"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(-0.14 0 0 -0.14 540 540)" id="7d0b8415-f1c6-4f03-ad7e-8f6914b92211"  >
<path style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: #32748c; fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-4490, -4424.22)" d="M 3600 7346 C 3510 7321 3431 7262 3382 7185 C 3368 7163 3249 6931 3117 6670 C 2985 6409 2728 5898 2545 5535 C 2362 5172 2090 4632 1940 4335 C 1790 4038 1524 3511 1350 3164 C 1029 2527 1020 2508 998 2468 C 936 2347 769 2006 756 1972 C 713 1858 754 1699 851 1601 C 898 1554 930 1535 1005 1508 C 1052 1491 1211 1490 4490 1490 C 7769 1490 7928 1491 7975 1508 C 8050 1535 8082 1554 8129 1601 C 8226 1699 8267 1858 8224 1972 C 8202 2031 8105 2223 7043 4330 C 6805 4801 6244 5915 5921 6555 C 5762 6871 5710 6927 5550 6955 C 5419 6977 5312 6943 5220 6851 C 5170 6801 5145 6755 4972 6410 C 4717 5903 4700 5870 4686 5870 C 4680 5871 4603 6016 4514 6193 C 4088 7046 4007 7201 3966 7241 C 3866 7341 3727 7381 3600 7346 z M 3906 5733 C 4015 5515 4124 5300 4148 5256 C 4171 5212 4190 5173 4190 5169 C 4190 5164 4204 5138 4221 5109 C 4237 5081 4253 5045 4256 5030 C 4261 5009 4189 4858 3907 4299 C 3479 3450 3004 2507 2925 2353 L 2868 2240 L 2305 2240 C 1924 2240 1739 2243 1735 2250 C 1731 2256 1733 2266 1738 2273 C 1743 2280 1895 2578 2075 2935 C 2578 3933 2956 4683 3310 5385 C 3488 5737 3645 6049 3659 6078 C 3672 6106 3689 6130 3695 6130 C 3701 6130 3796 5951 3906 5733 z M 5723 5280 C 5846 5035 6090 4552 6265 4205 C 6440 3859 6729 3285 6907 2930 C 7086 2575 7237 2279 7243 2272 C 7249 2264 7250 2256 7243 2249 C 7237 2243 7095 2241 6870 2242 L 6506 2245 L 6344 2565 C 6256 2741 6036 3178 5855 3535 C 5190 4852 5120 4994 5120 5015 C 5120 5031 5427 5659 5465 5720 C 5469 5726 5478 5730 5486 5728 C 5493 5726 5600 5525 5723 5280 z M 4725 4108 C 4749 4062 5099 3368 5531 2511 C 5601 2371 5657 2253 5654 2248 C 5646 2236 3720 2238 3720 2250 C 3720 2255 3862 2542 4037 2887 C 4211 3233 4426 3660 4514 3837 C 4603 4014 4680 4159 4686 4160 C 4692 4160 4710 4136 4725 4108 z" stroke-linecap="round" />
</g>
</svg>