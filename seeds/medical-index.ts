// seeds/medical-index.ts

import { connectDB } from "@/lib/db";
import { seedMedicalDomain } from './medical-seeder';

async function runMedicalSeeder() {
  try {
    console.log('🏥 Starting Medical Center Website seeder...');
    
    await connectDB();
    console.log('✅ Database connected successfully');
    
    const result = await seedMedicalDomain();
    
    console.log('🎉 Medical Center Website seeding completed successfully!');
    console.log('📊 Seeding Summary:');
    console.log(`   - Domain: ${result.domain.title_en}`);
    console.log(`   - Total Features: ${result.featuresCount}`);
    console.log(`   - Basic Features: ${result.basicFeaturesCount}`);
    console.log(`   - Starting Price: ${result.startingPrice} AED`);
    console.log(`   - Domain Slug: ${result.domain.slug}`);
    console.log(`   - Domain Status: ${result.domain.disabled ? 'Disabled' : 'Enabled'}`);
    
  } catch (error) {
    console.error('❌ Error running medical seeder:', error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

runMedicalSeeder();
