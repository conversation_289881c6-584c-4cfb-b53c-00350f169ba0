// seeds/seed-domains.ts

import Domain from '@/app/models/Domain'; // Adjust if your model path differs
export async function seedDomains() {
    const domains = [
        {
            title_en: "E-Commerce Platform",
            title_ar: "منصة التجارة الإلكترونية",
            description_en: "Professional e-commerce platforms with advanced inventory management, secure payments, and multi-language support to boost your online sales.",
            description_ar: "منصات تجارة إلكترونية احترافية مع إدارة متقدمة للمخزون ومدفوعات آمنة ودعم متعدد اللغات لزيادة مبيعاتك الإلكترونية.",
            preview_title_en: "Towards an Exceptional Digital Shopping Experience!",
            preview_title_ar: "نحو تجربة تسوق رقمية استثنائية!",
            preview_description_en: "We specialize in designing and developing e-commerce applications that help your brand stand out and achieve your business goals. Whether you're looking for a complete new store or want to enhance your customers' experience, we provide a high-quality solution that meets industry standards. With our expert team, we'll help you transform your online shopping platform into a high-impact store that exceeds your customers' expectations.",
            preview_description_ar: "نحن متخصصون في تصميم وتطوير تطبيقات التجارة الإلكترونية التي تساعد علامتك التجارية على التميز وتحقيق أهداف عملك. سواء كنت تبحث عن متجر جديد بالكامل أو ترغب في تحسين تجربة عملائك، فإننا نقدم حلاً عالي الجودة يلبي معايير الصناعة. مع فريقنا من الخبراء، سنساعدك على تحويل منصة التسوق عبر الإنترنت الخاصة بك إلى متجر عالي التأثير يتجاوز توقعات عملائك.",
            icon: "e-commerce.svg",
            screenshots: [
                "/images/screenshots/ecommerce-1.png",
                "/images/screenshots/ecommerce-2.png",
                "/images/screenshots/ecommerce-3.png",
                "/images/screenshots/ecommerce-4.png",
                "/images/screenshots/ecommerce-5.png",
                "/images/screenshots/ecommerce-6.png",
                "/images/screenshotsthe/ecommerce-7.png",
            ],
            slug: 'e-commerce',
            disabled: false,
            starting_price: 0,
        },

        {
            title_en: "Medical Center Website",
            title_ar: "موقع المركز الطبي",
            description_en: "Professional medical center websites with online appointment booking, doctor profiles, patient management, and consultation services to enhance patient experience.",
            description_ar: "مواقع احترافية للمراكز الطبية مع حجز المواعيد الإلكتروني وملفات الأطباء وإدارة المرضى وخدمات الاستشارة لتحسين تجربة المرضى.",
            preview_title_en: "Professional Medical Center Websites with Smart Appointment Systems!",
            preview_title_ar: "مواقع احترافية للمراكز الطبية مع أنظمة مواعيد ذكية!",
            preview_description_en: "We specialize in creating modern, user-friendly medical center websites that provide comprehensive information about your clinic and medical services. Our solutions include intelligent appointment booking systems, detailed doctor profiles, patient management, and online consultation services. With our medical website expertise, we help medical centers establish a strong digital presence, improve patient accessibility, and streamline appointment scheduling to meet modern healthcare expectations.",
            preview_description_ar: "نحن متخصصون في إنشاء مواقع حديثة وسهلة الاستخدام للمراكز الطبية توفر معلومات شاملة عن عيادتك والخدمات الطبية المقدمة. تشمل حلولنا أنظمة حجز مواعيد ذكية وملفات مفصلة للأطباء وإدارة المرضى وخدمات الاستشارة الإلكترونية. بخبرتنا في مواقع المراكز الطبية، نساعد المراكز الطبية على إنشاء حضور رقمي قوي وتحسين إمكانية وصول المرضى وتبسيط جدولة المواعيد لتلبية توقعات الرعاية الصحية الحديثة.",
            icon: "medical-website.svg",
            screenshots: [
                "/images/screenshots/medical-1.jpg",
                "/images/screenshots/medical-2.jpg",
                "/images/screenshots/medical-3.jpg",
                "/images/screenshots/medical-4.jpg"
            ],
            slug: 'medical',
            disabled: true,
            starting_price: 0,
        },

    ];

    // Add slug field

    await Domain.deleteMany()
    console.log('domains deleted');

    await Domain.insertMany(domains);
    console.log('✅ Domains seeded successfully!');
}
