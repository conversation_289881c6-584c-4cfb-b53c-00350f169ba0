// seeds/seed-features.ts

import { FeatureType } from '@/app/[locale]/pricing/components/FeaturesContainer';
import Domain from '@/app/models/Domain';
import Feature from '@/app/models/Feature'; // Adjust the path as needed
import mongoose from 'mongoose';

export async function seedFeatures() {
    const ecommerceDomain = await Domain.findOne({ slug: 'e-commerce' });

    if (!ecommerceDomain) {
        throw new Error('E-commerce domain not found. Please seed domains first.');
    }

    const domainId = ecommerceDomain._id;

    const features = [
        // Base Plan Features
        {
            title_en: "Product Management",
            title_ar: "إدارة المنتجات",
            description_en: "Allows businesses to add, edit, and organize products with various attributes (e.g., size, color), enabling better management and easy catalog updates.",
            description_ar: "يسمح للشركات بإضافة المنتجات وتعديلها وتنظيمها بخصائص متعددة (مثل الحجم، اللون)، مما يتيح إدارة أفضل وتحديث سهل للكتالوج.",
            type: "Basic",
            category: "ecommerce",
            role_category: "backend",
            base_price: 1880,
            web_time: 45.0,
            mobile_time: 80.0,
            backend_time: 60.0,
            design_time: 25.0,
            devops_time: 0,
            QA_time: 50.0,
            domain_id: domainId
        },
        {
            title_en: "Inventory Tracking",
            title_ar: "تتبع المخزون",
            description_en: "Real-time tracking of stock levels, automatic updates when products are sold, and alerts for low stock, ensuring businesses are always aware of inventory status.",
            description_ar: "تتبع مستويات المخزون في الوقت الفعلي، التحديث التلقائي عند بيع المنتجات، وتنبيهات عند انخفاض المخزون، مما يضمن أن الشركات على علم دائم بحالة المخزون.",
            type: "Basic",
            category: "ecommerce",
            role_category: "backend",
            base_price: 470,
            web_time: 10.0,
            mobile_time: 10.0,
            backend_time: 20.0,
            design_time: 4.0,
            devops_time: 0,
            QA_time: 20.0,
            domain_id: domainId
        },
        {
            title_en: "Customer Insights",
            title_ar: "تحليلات العملاء",
            description_en: "Provides detailed information on customer order history, profiles, and engagement, allowing businesses to better understand customer behavior and trends.",
            description_ar: "يوفر معلومات مفصلة عن تاريخ طلبات العملاء، ملفاتهم الشخصية، ومشاركتهم، مما يسمح للشركات بفهم أفضل لسلوك العملاء والاتجاهات.",
            type: "Optional",
            category: "analytics",
            role_category: "backend",
            base_price: 770,
            web_time: 15.0,
            mobile_time: 45.0,
            backend_time: 20.0,
            design_time: 10.0,
            devops_time: 0,
            QA_time: 15.0,
            domain_id: domainId
        },
        {
            title_en: "SEO Optimization",
            title_ar: "تحسين محركات البحث",
            description_en: "Enhances the app's visibility and ranking on app stores through optimized keywords, engaging descriptions, and compelling visuals, improving discoverability.",
            description_ar: "يعزز رؤية التطبيق وترتيبه في متاجر التطبيقات من خلال الكلمات الرئيسية المحسنة، الأوصاف الجذابة، والصور المقنعة، مما يحسن إمكانية الاكتشاف.",
            type: "Optional",
            category: "content",
            role_category: "web",
            base_price: 350,
            web_time: 0,
            mobile_time: 15.0,
            backend_time: 10.0,
            design_time: 0,
            devops_time: 0,
            QA_time: 20.0,
            domain_id: domainId
        },
        {
            title_en: "Basic Analytics & Reports",
            title_ar: "التحليلات والتقارير الأساسية",
            description_en: "Tracks sales performance and customer behavior with basic reports, enabling businesses to assess trends and make informed decisions.",
            description_ar: "يتتبع أداء المبيعات وسلوك العملاء مع تقارير أساسية، مما يمكن الشركات من تقييم الاتجاهات واتخاذ قرارات مستنيرة.",
            type: "Basic",
            category: "analytics",
            role_category: "general",
            base_price: 550,
            web_time: 20.0,
            mobile_time: 0,
            backend_time: 30.0,
            design_time: 8.0,
            devops_time: 0,
            QA_time: 15.0,
            domain_id: domainId
        },
        {
            title_en: "Guest Checkout",
            title_ar: "الدفع كضيف",
            description_en: "Enables customers to make purchases without requiring account creation, reducing friction in the checkout process and improving conversion rates.",
            description_ar: "يسمح للعملاء بإجراء عمليات الشراء دون الحاجة إلى إنشاء حساب، مما يقلل من الاحتكاك في عملية الدفع ويحسن معدلات التحويل.",
            type: "Optional",
            category: "checkout",
            role_category: "backend",
            base_price: 400,
            web_time: 0,
            mobile_time: 10.0,
            backend_time: 15.0,
            design_time: 2.0,
            devops_time: 0,
            QA_time: 2.0,
            domain_id: domainId
        },
        {
            title_en: "Reviews & Ratings",
            title_ar: "التقييمات والمراجعات",
            description_en: "Allows customers to leave feedback on products they've purchased, building social proof and assisting other buyers in making informed purchasing decisions.",
            description_ar: "يسمح للعملاء بترك تعليقات على المنتجات التي اشتروها، مما يبني دليلًا اجتماعيًا ويساعد المشترين الآخرين في اتخاذ قرارات شراء مستنيرة.",
            type: "Optional",
            category: "ecommerce",
            role_category: "backend",
            base_price: 500,
            web_time: 0,
            mobile_time: 30.0,
            backend_time: 25.0,
            design_time: 4.0,
            devops_time: 0,
            QA_time: 8.0,
            domain_id: domainId
        },
        {
            title_en: "Smart Filtering & Search",
            title_ar: "تصفية وبحث ذكي",
            description_en: "Advanced search capabilities with smart filters (e.g., size, price range, color) to help customers quickly find products that match their preferences.",
            description_ar: "إمكانيات بحث متقدمة مع عوامل تصفية ذكية (مثل الحجم، نطاق السعر، اللون) لمساعدة العملاء في العثور بسرعة على المنتجات التي تطابق تفضيلاتهم.",
            type: "Optional",
            category: "ecommerce",
            role_category: "backend",
            base_price: 400,
            web_time: 0,
            mobile_time: 8.0,
            backend_time: 30.0,
            design_time: 6.0,
            devops_time: 0,
            QA_time: 10.0,
            domain_id: domainId
        },
        {
            title_en: "User Authentication",
            title_ar: "مصادقة المستخدم",
            description_en: "Secure sign-up and login options via email, phone number, or social media, ensuring a secure and personalized experience for users.",
            description_ar: "خيارات تسجيل دخول وتسجيل آمنة عبر البريد الإلكتروني، رقم الهاتف، أو وسائل التواصل الاجتماعي، مما يضمن تجربة آمنة ومخصصة للمستخدمين.",
            type: "Basic",
            category: "authentication",
            role_category: "backend",
            base_price: 250,
            web_time: 6.0,
            mobile_time: 8.0,
            backend_time: 10.0,
            design_time: 4.0,
            devops_time: 0,
            QA_time: 2.0,
            domain_id: domainId
        },
        {
            title_en: "Wishlist & Save for Later",
            title_ar: "قائمة الرغبات وحفظ للمستقبل",
            description_en: "Allows users to save products they are interested in for future purchase, improving customer retention and encouraging repeat purchases.",
            description_ar: "يسمح للمستخدمين بحفظ المنتجات التي يهتمون بها لشرائها في المستقبل، مما يحسن الاحتفاظ بالعملاء ويشجع على عمليات الشراء المتكررة.",
            type: "Optional",
            category: "ecommerce",
            role_category: "general",
            base_price: 150,
            web_time: 0,
            mobile_time: 6.0,
            backend_time: 8.0,
            design_time: 2.0,
            devops_time: 0,
            QA_time: 1.0,
            domain_id: domainId
        },
        {
            title_en: "Order Management",
            title_ar: "إدارة الطلبات",
            description_en: "Full control over order processing, including tracking, updating, and managing orders from placement to delivery, ensuring smooth order fulfillment.",
            description_ar: "تحكم كامل في معالجة الطلبات، بما في ذلك تتبعها، تحديثها، وإدارتها من وضع الطلب حتى التسليم، مما يضمن تنفيذ سلس للطلبات.",
            type: "Basic",
            category: "order",
            role_category: "backend",
            base_price: 2000,
            web_time: 12.0,
            mobile_time: 8.0,
            backend_time: 16.0,
            design_time: 8.0,
            devops_time: 0,
            QA_time: 6.0,
            domain_id: domainId
        },
        {
            title_en: "Discounts & Coupons",
            title_ar: "الخصومات والكوبونات",
            description_en: "Create and manage promotional discount codes and automated price reductions for customer purchases, encouraging sales and loyalty.",
            description_ar: "إنشاء وإدارة رموز الخصم الترويجية وتخفيضات الأسعار التلقائية لعمليات شراء العملاء، مما يشجع المبيعات والولاء.",
            type: "Optional",
            category: "general",
            role_category: "backend",
            base_price: 1100,
            web_time: 12.0,
            mobile_time: 10.0,
            backend_time: 35.0,
            design_time: 3.0,
            devops_time: 0,
            QA_time: 8.0,
            domain_id: domainId
        },
        {
            title_en: "Multi-Currency Support",
            title_ar: "دعم العملات المتعددة",
            description_en: "Automatically converts product prices to the local currency of international shoppers, providing a seamless global shopping experience.",
            description_ar: "تحويل أسعار المنتجات تلقائيًا إلى العملة المحلية للمتسوقين الدوليين، مما يوفر تجربة تسوق عالمية سلسة.",
            type: "Optional",
            category: "international",
            role_category: "backend",
            base_price: 1250,
            web_time: 15.0,
            mobile_time: 15.0,
            backend_time: 30.0,
            design_time: 6.0,
            devops_time: 0,
            QA_time: 12.0,
            domain_id: domainId
        },
        {
            title_en: "Automated Tax Calculation",
            title_ar: "حساب الضرائب التلقائي",
            description_en: "Automatically calculates sales tax based on the customer's location, ensuring compliance with tax regulations and accurate pricing.",
            description_ar: "حساب ضريبة المبيعات تلقائيًا بناءً على موقع العميل، مما يضمن الامتثال للوائح الضريبية والتسعير الدقيق.",
            type: "Optional",
            category: "checkout",
            role_category: "backend",
            base_price: 1120,
            web_time: 18.0,
            mobile_time: 10.0,
            backend_time: 25.0,
            design_time: 3.0,
            devops_time: 0,
            QA_time: 15.0,
            domain_id: domainId
        },
        {
            title_en: "Marketing Notifications",
            title_ar: "إشعارات التسويق",
            description_en: "Control and manage marketing notifications (e.g., promotional offers, updates) sent to customers through multiple channels, including email, SMS, and push notifications.",
            description_ar: "التحكم في إشعارات التسويق وإدارتها (مثل العروض الترويجية، التحديثات) المرسلة إلى العملاء عبر قنوات متعددة، بما في ذلك البريد الإلكتروني، الرسائل القصيرة، وإشعارات الدفع.",
            type: "Optional",
            category: "notificationns",
            role_category: "backend",
            base_price: 1000,
            web_time: 15.0,
            mobile_time: 18.0,
            backend_time: 20.0,
            design_time: 4.0,
            devops_time: 0,
            QA_time: 8.0,
            domain_id: domainId
        },
        {
            title_en: "Role-Based Access for Admins",
            title_ar: "الوصول القائم على الأدوار للمشرفين",
            description_en: "Enables businesses to assign different roles and permissions to admin users, controlling access to various platform features and functions.",
            description_ar: "يمكن الشركات من تعيين أدوار وأذونات مختلفة لمستخدمي المشرفين، للتحكم في الوصول إلى ميزات ووظائف المنصة المختلفة.",
            type: "Optional",
            category: "admin",
            role_category: "backend",
            base_price: 730,
            web_time: 20.0,
            mobile_time: 0,
            backend_time: 20.0,
            design_time: 0,
            devops_time: 0,
            QA_time: 6.0,
            domain_id: domainId
        },
        {
            title_en: "Enhanced Analytics & Reports",
            title_ar: "تحليلات وتقارير محسنة",
            description_en: "Advanced reporting and analytics tools provide deeper insights into sales trends, customer behavior, and product performance for better business decisions.",
            description_ar: "توفر أدوات التقارير والتحليلات المتقدمة رؤى أعمق في اتجاهات المبيعات، سلوك العملاء، وأداء المنتجات لاتخاذ قرارات تجارية أفضل.",
            type: "Optional",
            category: "analytics",
            role_category: "backend",
            base_price: 900,
            web_time: 15.0,
            mobile_time: 0,
            backend_time: 30.0,
            design_time: 2.0,
            devops_time: 0,
            QA_time: 10.0,
            domain_id: domainId
        },
        {
            title_en: "Similar Product Suggestions",
            title_ar: "اقتراحات منتجات مشابهة",
            description_en: "Recommends products based on similar categories or styles to the ones a customer has viewed or purchased, increasing upsell and cross-sell opportunities.",
            description_ar: "يقترح منتجات بناءً على فئات أو أنماط مماثلة لتلك التي شاهدها العميل أو اشتراها، مما يزيد من فرص البيع الإضافي والبيع المتقاطع.",
            type: "Optional",
            category: "ecommerce",
            role_category: "backend",
            base_price: 815,
            web_time: 6.0,
            mobile_time: 10.0,
            backend_time: 25.0,
            design_time: 3.0,
            devops_time: 0,
            QA_time: 8.0,
            domain_id: domainId
        },

        // Start-Up Companies Plan Features
        {
            title_en: "Loyalty & Rewards Program",
            title_ar: "برنامج الولاء والمكافآت",
            description_en: "A built-in system that incentivizes repeat customers with reward points, discounts, or special offers for frequent purchases, increasing customer retention.",
            description_ar: "نظام مدمج يحفز العملاء المتكررين بنقاط مكافأة، خصومات، أو عروض خاصة للشراء المتكرر، مما يزيد من الاحتفاظ بالعملاء.",
            type: "Optional",
            category: "general",
            role_category: "backend",
            base_price: 2000,
            web_time: 30.0,
            mobile_time: 30.0,
            backend_time: 40.0,
            design_time: 12.0,
            devops_time: 0,
            QA_time: 16.0,
            domain_id: domainId
        },
        {
            title_en: "Advanced Analytics & Business Intelligence",
            title_ar: "تحليلات متقدمة وذكاء الأعمال",
            description_en: "AI-driven insights that provide predictive trends, custom reporting, and actionable business intelligence to optimize sales and marketing strategies.",
            description_ar: "رؤى مدعومة بالذكاء الاصطناعي توفر اتجاهات تنبؤية، تقارير مخصصة، وذكاء أعمال قابل للتنفيذ لتحسين استراتيجيات المبيعات والتسويق.",
            type: "Optional",
            category: "analytics",
            role_category: "backend",
            base_price: 750,
            web_time: 6.0,
            mobile_time: 0,
            backend_time: 40.0,
            design_time: 0,
            devops_time: 0,
            QA_time: 0,
            domain_id: domainId
        },






        {
            title_en: "Hosting (On Us)",
            title_ar: "استضافة (على حسابنا)",
            description_en: "We handle the app deployment and hosting under our account, ensuring that the app is fully set up, maintained, and optimized for performance and uptime.",
            description_ar: "نتولى نشر التطبيق واستضافته تحت حسابنا، مما يضمن إعداد التطبيق بالكامل، صيانته، وتحسينه للأداء وزمن التشغيل.",
            type: "Add_On",
            category: "Support",
            role_category: "devops",
            base_price: 130,
            web_time: 2.0,
            mobile_time: 2.0,
            backend_time: 3.0,
            design_time: 0,
            devops_time: 6.0,
            QA_time: 4.0,
            domain_id: domainId
        },
        {
            title_en: "Expanded Shipping Options",
            title_ar: "خيارات شحن موسعة",
            description_en: "Allows businesses to choose from multiple shipping providers (e.g., Zajel, Aramex) with flexible shipping rates and methods, offering better logistics management.",
            description_ar: "يسمح للشركات بالاختيار من بين عدة مزودي شحن (مثل Zajel، Aramex) مع أسعار وطرق شحن مرنة، مما يوفر إدارة لوجستية أفضل.",
            type: "Optional",
            category: "shipping",
            role_category: "backend",
            base_price: 560,
            web_time: 6.0,
            mobile_time: 6.0,
            backend_time: 35.0,
            design_time: 0,
            devops_time: 0,
            QA_time: 6.0,
            domain_id: domainId
        },
        {
            title_en: "Expanded Payment Options",
            title_ar: "خيارات دفع موسعة",
            description_en: "Expands payment methods to include multiple gateways such as PayPal, Apple Pay, etc., providing customers with more options to pay for their orders.",
            description_ar: "توسيع طرق الدفع لتشمل بوابات متعددة مثل PayPal، Apple Pay، إلخ، مما يوفر للعملاء المزيد من الخيارات لدفع طلباتهم.",
            type: "Optional",
            category: "payment",
            role_category: "general",
            base_price: 400,
            web_time: 4.0,
            mobile_time: 4.0,
            backend_time: 24.0,
            design_time: 0,
            devops_time: 0,
            QA_time: 4.0,
            domain_id: domainId
        },
        {
            title_en: "Professional Branding",
            title_ar: "العلامة التجارية الاحترافية",
            description_en: "Tailors the platform's design to match the company's unique brand identity, including logos, colors, and overall visual design, enhancing brand recognition.",
            description_ar: "تخصيص تصميم المنصة لتتناسب مع هوية العلامة التجارية الفريدة للشركة، بما في ذلك الشعارات، الألوان، والتصميم المرئي العام، مما يعزز التعرف على العلامة التجارية.",
            type: "Add_On",
            category: "Marketing",
            role_category: "design",
            base_price: 420,
            web_time: 0,
            mobile_time: 0,
            backend_time: 0,
            design_time: 40.0,
            devops_time: 0,
            QA_time: 0,
            domain_id: domainId
        },
        {
            title_en: "Standard Support",
            title_ar: "الدعم القياسي",
            description_en: "Provides standard support services with issue resolution upon request, available for up to 10 hours during the first month, ensuring smooth operations.",
            description_ar: "يوفر خدمات دعم قياسية مع حل المشكلات عند الطلب، متاح لمدة تصل إلى 10 ساعات خلال الشهر الأول، مما يضمن عمليات سلسة.",
            type: "Add_On",
            category: "Support",
            role_category: "managment",
            base_price: 220,
            web_time: 5.0,
            mobile_time: 5.0,
            backend_time: 5.0,
            design_time: 0,
            devops_time: 5.0,
            QA_time: 5.0,
            domain_id: domainId
        },
        {
            title_en: "Priority Support (10 hrs)",
            title_ar: "دعم الأولوية (10 ساعات)",
            description_en: "Priority support with guaranteed response times and issue resolution, providing businesses with focused, high-priority attention for critical issues.",
            description_ar: "دعم ذو أولوية مع أوقات استجابة مضمونة وحلول سريعة للمشكلات، مما يوفر للشركات اهتمامًا مركزًا وعالي الأولوية للقضايا الحرجة.",
            type: "Add_On",
            category: "Support",
            role_category: "managment",
            base_price: 220,
            web_time: 5.0,
            mobile_time: 5.0,
            backend_time: 5.0,
            design_time: 0,
            devops_time: 5.0,
            QA_time: 5.0,
            domain_id: domainId
        },
        {
            title_en: "Onboarding & Training",
            title_ar: "جلسات تهيئة و تدريب",
            description_en: "Includes initial onboarding sessions and training to ensure clients are familiar with the platform's features, functionality, and management tools.",
            description_ar: "يشمل جلسات تهيئة أولية وتدريبًا لضمان إلمام العملاء بميزات المنصة ووظائفها وأدوات إدارتها.",
            type: "Add_On",
            category: "Support",
            role_category: "managment",
            base_price: 110,
            web_time: 0,
            mobile_time: 0,
            backend_time: 0,
            design_time: 0,
            devops_time: 0,
            QA_time: 0,
            domain_id: domainId
        }
        // {
        //     title_en: "API & System Integration",
        //     title_ar: "تكامل API والنظام",
        //     description_en: "Provides integration with third-party systems such as CRMs, ERPs, payment gateways, and shipping providers via APIs, enabling a unified business ecosystem.",
        //     description_ar: "يوفر التكامل مع أنظمة الطرف الثالث مثل أنظمة CRM، ERP، بوابات الدفع، ومزودي الشحن عبر واجهات برمجة التطبيقات (APIs)، مما يتيح نظامًا بيئيًا موحدًا للأعمال.",
        //     type: "Optional",
        //     category: "integration",
        //     role_category: "backend",
        //     base_price: 0,
        //     web_time: 10.0,
        //     mobile_time: 10.0,
        //     backend_time: 40.0,
        //     design_time: 0,
        //     devops_time: 0,
        //     QA_time: 0,
        //     domain_id: domainId
        // }
    ];

    await Feature.deleteMany({ domain_id: domainId });
    console.log('Features deleted for domain:', domainId);
    const basicFeatures = features.filter(f => f.type === 'Basic');
    const startingPrice = basicFeatures.reduce((sum, f) => sum + f.base_price, 0);
    await Feature.insertMany(features);
    console.log('✅ Features seeded successfully for domain:', domainId);
    await Domain.findByIdAndUpdate(domainId, { starting_price: startingPrice });
    console.log('domain starting price updated successfuly');

}