// seeds/index.ts

import { connectDB } from "@/lib/db";
import { seedDomains } from './domain-seeder'
import { seedRoles } from "./role-seeder";
import { seedFeatures } from "./features-seed";

async function runSeeders() {
  await connectDB();
  await seedDomains();
  console.log('Domain Seeding complete!');
  await seedRoles()
  console.log('Role Seeding complete!');

  await seedFeatures()
  console.log('features seeded');
  
  process.exit()
}

runSeeders().catch(console.error);
