// seeds/seed-roles.ts

import Role from '@/app/models/Role'; // Adjust if your model path differs

export async function seedRoles() {
    const roles = [
        {
            title_en: "Project Manager",
            title_ar: "مدير المشروع",
            description_en: "Oversee the entire project, manage timelines, and communicate with the client.Coordinate between teams and ensure deliverables are met.",
            description_ar: "الإشراف على المشروع بالكامل، إدارة الجداول الزمنية، والتواصل مع العميل. التنسيق بين الفرق والتأكد من تسليم المهام فيالوقت المحدد.",
            cost: 1000,
            category: "management"
        },
        {
            title_en: "Designer",
            title_ar: "مصمم",
            description_en: "Design the user interface and ensure a seamless user experience.Create wireframes, prototypes, and final designs.",
            description_ar: "تصميم واجهة المستخدم وضمان تجربة مستخدم سلسة.  إنشاء النماذج التخطيطية (Wireframes)، والنماذج الأولية (Prototypes)، والتصميمات النهائية.",
            cost: 1000,
            category: "design"
        },
        {
            title_en: "Web Frontend Developer",
            title_ar: "مطوّر الواجهة الأمامية للويب",
            description_en: "Develop the user interface for the website.Ensure the website is responsive,user-friendly, and works across different browsers.Implement designs provided by the UI/UX designer.Optimize the website for performance and speed",
            description_ar: "تطوير واجهة المستخدم للمواقع الإلكترونية. ضمان أن يكون الموقع متجاوبًا، سهل الاستخدام، ويعمل عبر المتصفحات المختلفة. تنفيذ التصاميم المقدمة من مصمم واجهة المستخدم وتجربة المستخدم (UI/UX). تحسين الموقع من حيث الأداء والسرعة.",
            cost: 1000,
            category: "web"
        },
        {
            title_en: "Mobile Frontend Developer",
            title_ar: "مطوّر الواجهات الأمامية للموبايل",
            description_en: "Develop the user interface for the e-commerce mobile app. Ensure the app is responsive and user-friendly.",
            description_ar: "تطوير واجهة المستخدم لتطبيق التجارة الإلكترونية على الهاتف المحمول وضمان أن التطبيق سريع الاستجابة وسهل الاستخدام.",
            cost: 1000,
            category: "mobile"
        },
        {
            title_en: "Backend Developer",
            title_ar: "مطور الواجهات الخلفية",
            description_en: "Build server-side logic, databases, and APIs. Handle integrations with third-party services.",
            description_ar: "بناء منطق الخادم وقواعد البيانات وواجهات البرمجة (APIs) والتعامل مع التكاملات مع خدمات الطرف الثالث.",
            cost: 1000,
            category: "backend"
        },
        {
            title_en: "DevOps Engineer",
            title_ar: "مهندس ديفوبس",
            description_en: "Manage deployment, server infrastructure, and CI/CD pipelines. Ensure the platform is scalable and secure.",
            description_ar: "إدارة النشر، والبنية التحتية للخوادم، وخطوط CI/CD وضمان أن تكون المنصة قابلة للتطوير وآمنة.",
            cost: 1000,
            category: "devops"
        },
        {
            title_en: "Marketing Specialist",
            title_ar: "مختص تسويق",
            description_en: "Handle digital marketing tasks like SEO, Google Ads, and social media campaigns.",
            description_ar: "التعامل مع مهام التسويق الرقمي مثل SEO، إعلانات جوجل، وحملات وسائل التواصل الاجتماعي.",
            cost: 1000,
            category: "marketing"
        },
        {
            title_en: "Content Writer",
            title_ar: "كاتب محتوى",
            description_en: "Create content like product descriptions, blog posts, and marketing copy.",
            description_ar: "إنشاء محتوى مثل أوصاف المنتجات، المنشورات على المدونات، والنصوص التسويقية.",
            cost: 1000,
            category: "content"
        }
    ];

    await Role.deleteMany();
    console.log('roles deleted');

    await Role.insertMany(roles);
    console.log('✅ Roles seeded successfully!');
}