// seeds/medical-seeder.ts

import Domain from '@/app/models/Domain';
import Feature from '@/app/models/Feature';

export async function seedMedicalDomain() {
    // Update the existing medical domain with the new starting price and preview content
    const medicalDomain = await Domain.findOneAndUpdate(
        { slug: 'medical' },
        {
            starting_price: 5800, // Sum of all basic features: 1200+600+800+1000+700+900+500
            disabled: false, // Enable the domain
            preview_title_en: "Professional Medical Center Websites with Smart Appointment Systems!",
            preview_title_ar: "مواقع احترافية للمراكز الطبية مع أنظمة مواعيد ذكية!",
            preview_description_en: "Create a modern, user-friendly medical center website that provides comprehensive information about your clinic and medical services. Our solutions include intelligent appointment booking systems, detailed doctor profiles, patient management, and online consultation services designed to enhance patient experience and streamline your medical practice operations.",
            preview_description_ar: "أنشئ موقعاً حديثاً وسهل الاستخدام لمركزك الطبي يوفر معلومات شاملة عن عيادتك والخدمات الطبية المقدمة. تشمل حلولنا أنظمة حجز مواعيد ذكية وملفات مفصلة للأطباء وإدارة المرضى وخدمات الاستشارة الإلكترونية مصممة لتحسين تجربة المرضى وتبسيط عمليات ممارستك الطبية.",
            screenshots: [
                "/images/screenshots/medical-1.png",
                "/images/screenshots/medical-2.png",
                "/images/screenshots/medical-3.png",
                "/images/screenshots/medical-4.png"
            ]
        },
        { new: true }
    );

    if (!medicalDomain) {
        throw new Error('Medical domain not found. Please run the main seeder first.');
    }

    const domainId = medicalDomain._id;

    const medicalFeatures = [
        // Basic Features for Medical Center Website
        {
            title_en: "Appointment System",
            title_ar: "نظام المواعيد",
            description_en: "Complete appointment booking system allowing patients to schedule, reschedule, and manage appointments online with real-time availability and automated notifications.",
            description_ar: "نظام حجز مواعيد شامل يسمح للمرضى بجدولة وإعادة جدولة وإدارة المواعيد عبر الإنترنت مع التوفر في الوقت الفعلي والإشعارات التلقائية.",
            type: "Basic",
            category: "medical",
            role_category: "backend",
            base_price: 1200,
            web_time: 35.0,
            mobile_time: 40.0,
            backend_time: 50.0,
            design_time: 25.0,
            devops_time: 5.0,
            QA_time: 30.0,
            domain_id: domainId
        },
        {
            title_en: "Testimonials Section",
            title_ar: "قسم الشهادات",
            description_en: "Patient testimonials and reviews section showcasing success stories, patient experiences, and feedback to build trust and credibility.",
            description_ar: "قسم شهادات وتقييمات المرضى يعرض قصص النجاح وتجارب المرضى والتعليقات لبناء الثقة والمصداقية.",
            type: "Basic",
            category: "content",
            role_category: "frontend",
            base_price: 600,
            web_time: 20.0,
            mobile_time: 15.0,
            backend_time: 10.0,
            design_time: 25.0,
            devops_time: 0,
            QA_time: 15.0,
            domain_id: domainId
        },
        {
            title_en: "Doctors Section",
            title_ar: "قسم الأطباء",
            description_en: "Comprehensive doctors listing section displaying medical team information, specializations, qualifications, and experience in an organized layout.",
            description_ar: "قسم شامل لعرض الأطباء يظهر معلومات الفريق الطبي والتخصصات والمؤهلات والخبرة في تخطيط منظم.",
            type: "Basic",
            category: "content",
            role_category: "frontend",
            base_price: 800,
            web_time: 25.0,
            mobile_time: 20.0,
            backend_time: 15.0,
            design_time: 30.0,
            devops_time: 0,
            QA_time: 20.0,
            domain_id: domainId
        },
        {
            title_en: "Doctor Profile",
            title_ar: "ملف الطبيب",
            description_en: "Detailed individual doctor profile pages with biography, specializations, education, certifications, working hours, and appointment booking integration.",
            description_ar: "صفحات ملف شخصي مفصلة للأطباء تتضمن السيرة الذاتية والتخصصات والتعليم والشهادات وساعات العمل وتكامل حجز المواعيد.",
            type: "Basic",
            category: "medical",
            role_category: "frontend",
            base_price: 1000,
            web_time: 30.0,
            mobile_time: 25.0,
            backend_time: 20.0,
            design_time: 35.0,
            devops_time: 0,
            QA_time: 25.0,
            domain_id: domainId
        },
        {
            title_en: "Services Section",
            title_ar: "قسم الخدمات",
            description_en: "Comprehensive medical services section showcasing all treatments, procedures, and healthcare services offered by the medical center with detailed descriptions.",
            description_ar: "قسم خدمات طبية شامل يعرض جميع العلاجات والإجراءات والخدمات الصحية التي يقدمها المركز الطبي مع أوصاف مفصلة.",
            type: "Basic",
            category: "content",
            role_category: "frontend",
            base_price: 700,
            web_time: 22.0,
            mobile_time: 18.0,
            backend_time: 12.0,
            design_time: 28.0,
            devops_time: 0,
            QA_time: 18.0,
            domain_id: domainId
        },
        {
            title_en: "Videos Section",
            title_ar: "قسم الفيديوهات",
            description_en: "Video gallery section featuring medical center tours, doctor introductions, patient testimonials, and educational health content with responsive video player.",
            description_ar: "قسم معرض الفيديوهات يعرض جولات في المركز الطبي وتعريف بالأطباء وشهادات المرضى والمحتوى التعليمي الصحي مع مشغل فيديو متجاوب.",
            type: "Basic",
            category: "media",
            role_category: "frontend",
            base_price: 900,
            web_time: 28.0,
            mobile_time: 22.0,
            backend_time: 15.0,
            design_time: 32.0,
            devops_time: 5.0,
            QA_time: 22.0,
            domain_id: domainId
        },
        {
            title_en: "Banners Slider",
            title_ar: "شريط الإعلانات المتحرك",
            description_en: "Dynamic banner slider for homepage featuring medical center highlights, special offers, health tips, and important announcements with smooth transitions.",
            description_ar: "شريط إعلانات ديناميكي للصفحة الرئيسية يعرض أبرز ما في المركز الطبي والعروض الخاصة والنصائح الصحية والإعلانات المهمة مع انتقالات سلسة.",
            type: "Basic",
            category: "design",
            role_category: "frontend",
            base_price: 500,
            web_time: 18.0,
            mobile_time: 15.0,
            backend_time: 8.0,
            design_time: 25.0,
            devops_time: 0,
            QA_time: 15.0,
            domain_id: domainId
        },

        // Optional Features



        {
            title_en: "Multi-language Support",
            title_ar: "دعم متعدد اللغات",
            description_en: "Complete multilingual support for Arabic and English interfaces with RTL/LTR layout optimization.",
            description_ar: "دعم متعدد اللغات كامل للواجهات العربية والإنجليزية مع تحسين تخطيط RTL/LTR.",
            type: "Optional",
            category: "localization",
            role_category: "frontend",
            base_price: 800,
            web_time: 30.0,
            mobile_time: 35.0,
            backend_time: 15.0,
            design_time: 20.0,
            devops_time: 0,
            QA_time: 20.0,
            domain_id: domainId
        },

        // Add-On Features
        {
            title_en: "Advanced Analytics Dashboard",
            title_ar: "لوحة تحليلات متقدمة",
            description_en: "Comprehensive analytics dashboard with patient flow analysis, revenue tracking, and performance metrics.",
            description_ar: "لوحة تحليلات شاملة مع تحليل تدفق المرضى وتتبع الإيرادات ومقاييس الأداء.",
            type: "Add_On",
            category: "analytics",
            role_category: "backend",
            base_price: 1500,
            web_time: 35.0,
            mobile_time: 25.0,
            backend_time: 50.0,
            design_time: 25.0,
            devops_time: 5.0,
            QA_time: 30.0,
            domain_id: domainId
        },

        {
            title_en: "24/7 Technical Support",
            title_ar: "دعم فني على مدار الساعة",
            description_en: "Round-the-clock technical support with guaranteed response times and priority issue resolution.",
            description_ar: "دعم فني على مدار الساعة مع أوقات استجابة مضمونة وحل أولوي للمشاكل.",
            type: "Basic",
            category: "support",
            role_category: "support",
            base_price: 500,
            web_time: 0,
            mobile_time: 0,
            backend_time: 0,
            design_time: 0,
            devops_time: 10.0,
            QA_time: 5.0,
            domain_id: domainId
        }
    ];

    // Delete existing features for this domain
    await Feature.deleteMany({ domain_id: domainId });
    console.log('Medical features deleted for domain:', domainId);

    // Calculate starting price from basic features
    const basicFeatures = medicalFeatures.filter(f => f.type === 'Basic');
    // const calculatedStartingPrice = basicFeatures.reduce((sum, f) => sum + f.base_price, 0);

    // Insert new features
    await Feature.insertMany(medicalFeatures);
    console.log('✅ Medical features seeded successfully for domain:', domainId);

    // Update domain with calculated starting price (should be 8000 based on basic features)
    // await Domain.findByIdAndUpdate(domainId, { starting_price: calculatedStartingPrice });
    // console.log('Medical domain starting price updated to:', calculatedStartingPrice);

    // Calculate actual starting price from basic features
    const calculatedStartingPrice = basicFeatures.reduce((sum, f) => sum + f.base_price, 0);

    return {
        domain: medicalDomain,
        featuresCount: medicalFeatures.length,
        basicFeaturesCount: basicFeatures.length,
        startingPrice: calculatedStartingPrice,
    };
}
