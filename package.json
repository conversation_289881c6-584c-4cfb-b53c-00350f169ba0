{"name": "gotru-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "seed": "npx tsx  seeds/index.ts", "seed:medical": "npx tsx seeds/medical-index.ts", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.1.8", "@heroicons/react": "^2.1.5", "@microsoft/clarity": "^1.0.0", "@nextui-org/dropdown": "^2.1.31", "@nextui-org/scroll-shadow": "^2.1.19", "@nextui-org/system": "^2.2.5", "@nextui-org/theme": "^2.2.9", "@prisma/client": "^6.3.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-query": "^5.66.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "framer-motion": "^11.3.28", "i18next": "^23.15.1", "i18next-resources-to-backend": "^1.2.1", "lucide-react": "^0.474.0", "mongoose": "^8.9.6", "next": "14.2.5", "next-i18n-router": "^5.5.1", "nodemailer": "^6.10.0", "pdf-lib": "^1.17.1", "radix-ui": "^1.1.2", "rc-steps": "^6.0.1", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.1", "react-i18next": "^15.0.1", "react-responsive": "^10.0.0", "react-scroll": "^1.9.0", "slugify": "^1.6.6", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^20.17.30", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-scroll": "^1.8.10", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "prisma": "^6.3.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.8.3"}}