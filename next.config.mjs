/** @type {import('next').NextConfig} */
const nextConfig = {
    images: {
        minimumCacheTTL: 31536000,
    },
    async redirects() {
        return [
          // Redirect /en routes to root paths
          {
            source: '/en',
            destination: '/',
            permanent: true,
          },
          {
            source: '/en/faq',
            destination: '/faq',
            permanent: true,
          },
          {
            source: '/en/portfolio',
            destination: '/portfolio',
            permanent: true,
          },
          {
            source: '/en/pricing/:path*',
            destination: '/pricing/:path*',
            permanent: true,
          },
          // Block suspicious query parameters
          {
            source: '/',
            has: [
              {
                type: 'query',
                key: 'whoisdatacenter.com',
              },
            ],
            destination: '/',
            permanent: true,
          },
          // Redirect any remaining /en paths
          {
            source: '/en/:path*',
            destination: '/:path*',
            permanent: true,
          },
        ];
    },
    async headers() {
        return [
          // Image caching headers
          {
            source: '/:path*.{jpg,jpeg,png,gif,webp,svg,ico}',
            headers: [
              {
                key: 'Cache-Control',
                value: 'public, max-age=31536000, immutable',
              },
              {
                key: 'Expires',
                value: new Date(Date.now() + 31536000 * 1000).toUTCString(),
              },
            ],
          },
          // Security headers for all pages
          {
            source: '/(.*)',
            headers: [
              {
                key: 'X-Frame-Options',
                value: 'DENY',
              },
              {
                key: 'X-Content-Type-Options',
                value: 'nosniff',
              },
              {
                key: 'Referrer-Policy',
                value: 'strict-origin-when-cross-origin',
              },
              {
                key: 'Strict-Transport-Security',
                value: 'max-age=31536000; includeSubDomains',
              },
            ],
          },
        ];
      },
};

export default nextConfig;
