import { NextResponse } from "next/server";
import { ApiError } from "./ApiError";

export function handleApiError(error: unknown) {
  if (error instanceof ApiError) {
    return NextResponse.json(
      {
        data: { error: error.message },
      },
      { status: error.statusCode }
    );
  }

  console.error("Unhandled API Error:", error);
  return NextResponse.json(
    {
      data: { error: "Internal Server Error" },
    },
    { status: 500 }
  );
}
