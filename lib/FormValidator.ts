import { z } from 'zod';

// Define the schema
const formSchema = z.object({
  phone: z.string().min(0).startsWith('+', { message: "please enter country code (e.g: +971)" }),
  email: z.string().min(1,{message: "email is required"}).email({ message: "Invalid email address" }),
  message: z.string().min(1, { message: "Message is required" }),
});

// Infer the type from the schema
export type FormSchema = z.infer<typeof formSchema>;

// Validation function
export function validateForm(data: FormSchema): { isValid: boolean; errors?: Partial<Record<keyof FormSchema, string>> } {
  const result = formSchema.safeParse(data);

  if (!result.success) {
    // Format errors into a more usable structure
    const errors: Partial<Record<keyof FormSchema, string>> = {};
    result.error.issues.forEach((issue) => {
      const key = issue.path[0] as keyof FormSchema; // Extract the field name (e.g., "phone", "email", "message")
      errors[key] = issue.message; // Assign the error message to the corresponding field
    });
    return { isValid: false, errors };
  }

  return { isValid: true };
}
