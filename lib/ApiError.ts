
export class ApiError extends Error {
  statusCode: number;

  constructor(statusCode: number, message: string) {
    super(message);
    this.statusCode = statusCode;
    this.name = "ApiError";

    Object.setPrototypeOf(this, ApiError.prototype);
  }

  static badRequest(msg: string="Missing Credentials") {
    return new ApiError(400, msg);
  }

  static unauthorized(msg: string = "Unauthorized") {
    return new ApiError(401, msg);
  }

  static forbidden(msg: string = "Forbidden") {
    return new ApiError(403, msg);
  }

  static notFound(msg: string = "Not Found") {
    return new ApiError(404, msg);
  }

  static internal(msg: string = "Internal Server Error") {
    return new ApiError(500, msg);
  }
}
